{"version": 3, "sources": ["../../../../src/database/typeorm/seeds/user.seed.ts"], "sourcesContent": ["import { DataSource } from 'typeorm';\nimport { Seeder, SeederFactoryManager } from 'typeorm-extension';\nimport { AccountEntity } from '~/database/typeorm/entities/account.entity';\nimport { RoleEntity } from '~/database/typeorm/entities/role.entity';\nimport { UserEntity } from '~/database/typeorm/entities/user.entity';\nimport { UsersRoleEntity } from '~/database/typeorm/entities/usersRole.entity';\n\nexport default class UserSeeder implements Seeder {\n    public async run(dataSource: DataSource, factoryManager: SeederFactoryManager): Promise<any> {\n        console.log('UserSeeder is running...');\n        const repository = dataSource.getRepository(UserEntity);\n        const accountRepo = dataSource.getRepository(AccountEntity);\n        const roleRepo = dataSource.getRepository(RoleEntity);\n        const usersRoleRepo = dataSource.getRepository(UsersRoleEntity);\n\n        const adminAccount = await accountRepo.findOneBy({ username: 'admin' });\n        const userAccount = await accountRepo.findOneBy({ username: 'user' });\n        const adminRole = await roleRepo.findOneBy({ name: 'Admin' });\n        const userRole = await roleRepo.findOneBy({ name: 'User' });\n\n        console.log('Debug - Admin account found:', adminAccount ? `ID: ${adminAccount.id}` : 'NOT FOUND');\n        console.log('Debug - User account found:', userAccount ? `ID: ${userAccount.id}` : 'NOT FOUND');\n        console.log('Debug - Admin role found:', adminRole ? `ID: ${adminRole.id}, Name: ${adminRole.name}` : 'NOT FOUND');\n        console.log('Debug - User role found:', userRole ? `ID: ${userRole.id}, Name: ${userRole.name}` : 'NOT FOUND');\n\n        // Create admin user\n        const existingAdminUser = await repository.findOneBy({ accountId: adminAccount?.id });\n        console.log('Debug - Existing admin user:', existingAdminUser ? `ID: ${existingAdminUser.id}` : 'NOT FOUND');\n\n        if (!existingAdminUser && adminAccount && adminRole) {\n            // Create admin user without single role\n            const adminUser = await repository.save({\n                accountId: adminAccount.id,\n                hoTen: 'Admin',\n                email: '<EMAIL>',\n            });\n\n            console.log('Debug - Admin user created:', `ID: ${adminUser.id}, AccountID: ${adminUser.accountId}`);\n\n            // Assign admin role through users_roles table\n            const adminUserRole = await usersRoleRepo.save({\n                userId: adminUser.id,\n                roleId: adminRole.id,\n                isPrimary: true,\n            });\n\n            console.log(\n                'Debug - Admin UserRole created:',\n                `UserID: ${adminUserRole.userId}, RoleID: ${adminUserRole.roleId}, IsPrimary: ${adminUserRole.isPrimary}`,\n            );\n            console.log('Admin user created with multiple roles system');\n        } else {\n            console.log('Admin user creation skipped - User already exists or missing dependencies');\n        }\n\n        // Create test user\n        const existingTestUser = await repository.findOneBy({ accountId: userAccount?.id });\n        console.log('Debug - Existing test user:', existingTestUser ? `ID: ${existingTestUser.id}` : 'NOT FOUND');\n\n        if (!existingTestUser && userAccount && userRole) {\n            // Create test user\n            const testUser = await repository.save({\n                accountId: userAccount.id,\n                hoTen: 'Test User',\n                email: '<EMAIL>',\n            });\n\n            console.log('Debug - Test user created:', `ID: ${testUser.id}, AccountID: ${testUser.accountId}`);\n\n            // Assign user role through users_roles table\n            const testUserRole = await usersRoleRepo.save({\n                userId: testUser.id,\n                roleId: userRole.id,\n                isPrimary: true,\n            });\n\n            console.log(\n                'Debug - Test UserRole created:',\n                `UserID: ${testUserRole.userId}, RoleID: ${testUserRole.roleId}, IsPrimary: ${testUserRole.isPrimary}`,\n            );\n            console.log('Test user created with User role');\n        } else {\n            console.log('Test user creation skipped - User already exists or missing dependencies');\n        }\n\n        console.log('UserSeeder is done!');\n    }\n}\n"], "names": ["UserSeeder", "run", "dataSource", "factoryManager", "console", "log", "repository", "getRepository", "UserEntity", "accountRepo", "AccountEntity", "roleRepo", "RoleEntity", "usersRoleRepo", "UsersRoleEntity", "adminAccount", "findOneBy", "username", "userAccount", "adminRole", "name", "userRole", "id", "existingAdminUser", "accountId", "adminUser", "save", "hoTen", "email", "adminUserRole", "userId", "roleId", "isPrimary", "existingTestUser", "testUser", "testUserRole"], "mappings": "qJAOqBA,2CALS,wDACH,qDACA,0DACK,gCAEjB,IAAA,AAAMA,WAAN,MAAMA,WACjB,MAAaC,IAAIC,UAAsB,CAAEC,cAAoC,CAAgB,CACzFC,QAAQC,GAAG,CAAC,4BACZ,MAAMC,WAAaJ,WAAWK,aAAa,CAACC,sBAAU,EACtD,MAAMC,YAAcP,WAAWK,aAAa,CAACG,4BAAa,EAC1D,MAAMC,SAAWT,WAAWK,aAAa,CAACK,sBAAU,EACpD,MAAMC,cAAgBX,WAAWK,aAAa,CAACO,gCAAe,EAE9D,MAAMC,aAAe,MAAMN,YAAYO,SAAS,CAAC,CAAEC,SAAU,OAAQ,GACrE,MAAMC,YAAc,MAAMT,YAAYO,SAAS,CAAC,CAAEC,SAAU,MAAO,GACnE,MAAME,UAAY,MAAMR,SAASK,SAAS,CAAC,CAAEI,KAAM,OAAQ,GAC3D,MAAMC,SAAW,MAAMV,SAASK,SAAS,CAAC,CAAEI,KAAM,MAAO,GAEzDhB,QAAQC,GAAG,CAAC,+BAAgCU,aAAe,CAAC,IAAI,EAAEA,aAAaO,EAAE,CAAC,CAAC,CAAG,aACtFlB,QAAQC,GAAG,CAAC,8BAA+Ba,YAAc,CAAC,IAAI,EAAEA,YAAYI,EAAE,CAAC,CAAC,CAAG,aACnFlB,QAAQC,GAAG,CAAC,4BAA6Bc,UAAY,CAAC,IAAI,EAAEA,UAAUG,EAAE,CAAC,QAAQ,EAAEH,UAAUC,IAAI,CAAC,CAAC,CAAG,aACtGhB,QAAQC,GAAG,CAAC,2BAA4BgB,SAAW,CAAC,IAAI,EAAEA,SAASC,EAAE,CAAC,QAAQ,EAAED,SAASD,IAAI,CAAC,CAAC,CAAG,aAGlG,MAAMG,kBAAoB,MAAMjB,WAAWU,SAAS,CAAC,CAAEQ,UAAWT,cAAcO,EAAG,GACnFlB,QAAQC,GAAG,CAAC,+BAAgCkB,kBAAoB,CAAC,IAAI,EAAEA,kBAAkBD,EAAE,CAAC,CAAC,CAAG,aAEhG,GAAI,CAACC,mBAAqBR,cAAgBI,UAAW,CAEjD,MAAMM,UAAY,MAAMnB,WAAWoB,IAAI,CAAC,CACpCF,UAAWT,aAAaO,EAAE,CAC1BK,MAAO,QACPC,MAAO,iBACX,GAEAxB,QAAQC,GAAG,CAAC,8BAA+B,CAAC,IAAI,EAAEoB,UAAUH,EAAE,CAAC,aAAa,EAAEG,UAAUD,SAAS,CAAC,CAAC,EAGnG,MAAMK,cAAgB,MAAMhB,cAAca,IAAI,CAAC,CAC3CI,OAAQL,UAAUH,EAAE,CACpBS,OAAQZ,UAAUG,EAAE,CACpBU,UAAW,IACf,GAEA5B,QAAQC,GAAG,CACP,kCACA,CAAC,QAAQ,EAAEwB,cAAcC,MAAM,CAAC,UAAU,EAAED,cAAcE,MAAM,CAAC,aAAa,EAAEF,cAAcG,SAAS,CAAC,CAAC,EAE7G5B,QAAQC,GAAG,CAAC,gDAChB,KAAO,CACHD,QAAQC,GAAG,CAAC,4EAChB,CAGA,MAAM4B,iBAAmB,MAAM3B,WAAWU,SAAS,CAAC,CAAEQ,UAAWN,aAAaI,EAAG,GACjFlB,QAAQC,GAAG,CAAC,8BAA+B4B,iBAAmB,CAAC,IAAI,EAAEA,iBAAiBX,EAAE,CAAC,CAAC,CAAG,aAE7F,GAAI,CAACW,kBAAoBf,aAAeG,SAAU,CAE9C,MAAMa,SAAW,MAAM5B,WAAWoB,IAAI,CAAC,CACnCF,UAAWN,YAAYI,EAAE,CACzBK,MAAO,YACPC,MAAO,gBACX,GAEAxB,QAAQC,GAAG,CAAC,6BAA8B,CAAC,IAAI,EAAE6B,SAASZ,EAAE,CAAC,aAAa,EAAEY,SAASV,SAAS,CAAC,CAAC,EAGhG,MAAMW,aAAe,MAAMtB,cAAca,IAAI,CAAC,CAC1CI,OAAQI,SAASZ,EAAE,CACnBS,OAAQV,SAASC,EAAE,CACnBU,UAAW,IACf,GAEA5B,QAAQC,GAAG,CACP,iCACA,CAAC,QAAQ,EAAE8B,aAAaL,MAAM,CAAC,UAAU,EAAEK,aAAaJ,MAAM,CAAC,aAAa,EAAEI,aAAaH,SAAS,CAAC,CAAC,EAE1G5B,QAAQC,GAAG,CAAC,mCAChB,KAAO,CACHD,QAAQC,GAAG,CAAC,2EAChB,CAEAD,QAAQC,GAAG,CAAC,sBAChB,CACJ"}