{"version": 3, "sources": ["../../../../src/database/typeorm/seeds/user.seed.ts"], "sourcesContent": ["import { DataSource } from 'typeorm';\nimport { Seeder, SeederFactoryManager } from 'typeorm-extension';\nimport { AccountEntity } from '~/database/typeorm/entities/account.entity';\nimport { RoleEntity } from '~/database/typeorm/entities/role.entity';\nimport { UserEntity } from '~/database/typeorm/entities/user.entity';\nimport { UsersRoleEntity } from '~/database/typeorm/entities/usersRole.entity';\n\nexport default class UserSeeder implements Seeder {\n    public async run(dataSource: DataSource, factoryManager: SeederFactoryManager): Promise<any> {\n        console.log('UserSeeder is running...');\n        const repository = dataSource.getRepository(UserEntity);\n        const accountRepo = dataSource.getRepository(AccountEntity);\n        const roleRepo = dataSource.getRepository(RoleEntity);\n        const usersRoleRepo = dataSource.getRepository(UsersRoleEntity);\n\n        const account = await accountRepo.findOneBy({ username: 'admin' });\n        const adminRole = await roleRepo.findOneBy({ name: 'Admin' });\n\n        // Check if admin user already exists\n        const existingUser = await repository.findOneBy({ accountId: account?.id });\n\n        if (!existingUser && account && adminRole) {\n            // Create admin user without single role\n            const user = await repository.save({\n                accountId: account.id,\n                hoTen: 'Admin',\n                email: '<EMAIL>',\n            });\n\n            // Assign admin role through users_roles table\n            await usersRoleRepo.save({\n                userId: user.id,\n                roleId: adminRole.id,\n                isPrimary: true,\n            });\n\n            console.log('Admin user created with multiple roles system');\n        }\n\n        console.log('UserSeeder is done!');\n    }\n}\n"], "names": ["UserSeeder", "run", "dataSource", "factoryManager", "console", "log", "repository", "getRepository", "UserEntity", "accountRepo", "AccountEntity", "roleRepo", "RoleEntity", "usersRoleRepo", "UsersRoleEntity", "account", "findOneBy", "username", "adminRole", "name", "existingUser", "accountId", "id", "user", "save", "hoTen", "email", "userId", "roleId", "isPrimary"], "mappings": "qJAOqBA,2CALS,wDACH,qDACA,0DACK,gCAEjB,IAAA,AAAMA,WAAN,MAAMA,WACjB,MAAaC,IAAIC,UAAsB,CAAEC,cAAoC,CAAgB,CACzFC,QAAQC,GAAG,CAAC,4BACZ,MAAMC,WAAaJ,WAAWK,aAAa,CAACC,sBAAU,EACtD,MAAMC,YAAcP,WAAWK,aAAa,CAACG,4BAAa,EAC1D,MAAMC,SAAWT,WAAWK,aAAa,CAACK,sBAAU,EACpD,MAAMC,cAAgBX,WAAWK,aAAa,CAACO,gCAAe,EAE9D,MAAMC,QAAU,MAAMN,YAAYO,SAAS,CAAC,CAAEC,SAAU,OAAQ,GAChE,MAAMC,UAAY,MAAMP,SAASK,SAAS,CAAC,CAAEG,KAAM,OAAQ,GAG3D,MAAMC,aAAe,MAAMd,WAAWU,SAAS,CAAC,CAAEK,UAAWN,SAASO,EAAG,GAEzE,GAAI,CAACF,cAAgBL,SAAWG,UAAW,CAEvC,MAAMK,KAAO,MAAMjB,WAAWkB,IAAI,CAAC,CAC/BH,UAAWN,QAAQO,EAAE,CACrBG,MAAO,QACPC,MAAO,iBACX,EAGA,OAAMb,cAAcW,IAAI,CAAC,CACrBG,OAAQJ,KAAKD,EAAE,CACfM,OAAQV,UAAUI,EAAE,CACpBO,UAAW,IACf,GAEAzB,QAAQC,GAAG,CAAC,gDAChB,CAEAD,QAAQC,GAAG,CAAC,sBAChB,CACJ"}