import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEmail, IsNotEmpty, IsOptional, IsArray, IsNumber } from 'class-validator';

export class CreateUserDto {
    @ApiProperty()
    @IsNotEmpty({ message: 'Tên đăng nhập không được để trống' })
    username: string;

    @ApiProperty()
    @IsNotEmpty({ message: '<PERSON><PERSON>t khẩu không được để trống' })
    password: string;

    @ApiProperty()
    @IsNotEmpty({ message: 'Email không được để trống' })
    @IsEmail({}, { message: '<PERSON>ail không hợp lệ' })
    email: string;

    @ApiProperty()
    @IsOptional()
    roleId: number;

    @ApiProperty({
        description: 'Array of role IDs for multiple roles (optional)',
        example: [1, 2, 3],
        type: [Number],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: 'Role IDs phải là một mảng' })
    @IsNumber({}, { each: true, message: 'Mỗi role ID phải là một số' })
    roleIds?: number[];

    @ApiProperty()
    @IsOptional()
    departmentId: number;

    @ApiProperty()
    @IsOptional()
    fullName: string;

    @ApiProperty()
    @IsOptional()
    areaCode: string;

    @ApiProperty()
    @IsOptional()
    phone: string;

    @ApiProperty()
    @IsOptional()
    address: string;

    @ApiProperty()
    @IsOptional()
    birthday: string;

    @ApiProperty()
    @IsOptional()
    @Transform(({ value }) => value?.toLowerCase())
    gender: string;
}
