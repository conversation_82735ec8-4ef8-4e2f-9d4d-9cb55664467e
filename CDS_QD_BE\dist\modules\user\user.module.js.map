{"version": 3, "sources": ["../../../src/modules/user/user.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\nimport { MediaService } from '~/modules/media/media.service';\nimport { UserController } from './user.controller';\nimport { UserService } from './user.service';\nimport { UsersRoleService } from './users-role.service';\n\n@Module({\n    imports: [],\n    controllers: [UserController],\n    providers: [UserService, UsersRoleService, MediaService],\n})\nexport class UserModule {}\n"], "names": ["UserModule", "imports", "controllers", "UserController", "providers", "UserService", "UsersRoleService", "MediaService"], "mappings": "oGAWaA,oDAAAA,oCAXU,8CACM,wDACE,gDACH,kDACK,0cAO1B,IAAA,AAAMA,WAAN,MAAMA,WAAY,+CAJrBC,QAAS,EAAE,CACXC,YAAa,CAACC,8BAAc,CAAC,CAC7BC,UAAW,CAACC,wBAAW,CAAEC,kCAAgB,CAAEC,0BAAY,CAAC"}