import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiTags, ApiOperation } from '@nestjs/swagger';
import { Permission } from '~/common/decorators/permission.decorator';
import { FilterDto } from '~/common/dtos/filter.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { AssignRolesDto } from './dto/assign-roles.dto';
import { UserService } from './user.service';

@ApiTags('User')
@ApiBearerAuth()
@Controller('user')
export class UserController {
    constructor(private readonly userService: UserService) {}

    @Permission('user:create')
    @Post()
    create(@Body() createUserDto: CreateUserDto) {
        return this.userService.create(createUserDto);
    }

    @Permission('user:findAll')
    @Get()
    @ApiQuery({ type: FilterDto })
    findAll(@Query() queries) {
        return this.userService.findAll({ ...queries });
    }

    @Permission('user:findOne')
    @Get(':id')
    findOne(@Param('id') id: string) {
        return this.userService.findOne(+id);
    }

    @Permission('user:update')
    @Patch(':id')
    update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
        return this.userService.update(+id, updateUserDto);
    }

    @Permission('user:remove')
    @Delete(':id')
    remove(@Param('id') id: string) {
        return this.userService.remove(+id);
    }

    // Multiple roles management endpoints
    @Permission('user:findRoles')
    @Get(':id/roles')
    @ApiOperation({ summary: 'Get all roles for a user' })
    getUserRoles(@Param('id') id: string) {
        return this.userService.getAllUserRoles(+id);
    }

    @Permission('user:assignRoles')
    @Post(':id/roles')
    @ApiOperation({ summary: 'Assign multiple roles to a user' })
    assignRolesToUser(@Param('id') id: string, @Body() assignRolesDto: AssignRolesDto) {
        return this.userService.assignRolesToUser(+id, assignRolesDto.roleIds);
    }

    @Permission('user:removeRole')
    @Delete(':id/roles/:roleId')
    @ApiOperation({ summary: 'Remove a specific role from user' })
    removeRoleFromUser(@Param('id') id: string, @Param('roleId') roleId: string) {
        return this.userService.removeRoleFromUser(+id, +roleId);
    }
}
