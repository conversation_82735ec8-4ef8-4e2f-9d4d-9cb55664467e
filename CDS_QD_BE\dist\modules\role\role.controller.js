"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"RoleController",{enumerable:true,get:function(){return RoleController}});const _common=require("@nestjs/common");const _swagger=require("@nestjs/swagger");const _permissiondecorator=require("../../common/decorators/permission.decorator");const _filterdto=require("../../common/dtos/filter.dto");const _services=require("../../shared/services");const _createroledto=require("./dto/create-role.dto");const _updateroledto=require("./dto/update-role.dto");const _roleservice=require("./role.service");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}function _ts_param(paramIndex,decorator){return function(target,key){decorator(target,key,paramIndex)}}let RoleController=class RoleController{create(createRoleDto){return this.roleService.create(createRoleDto)}findAll(queries){return this.roleService.findAll({...queries})}findOne(id){return this.roleService.findOne(+id)}update(id,updateRoleDto){return this.roleService.update(+id,updateRoleDto)}remove(id){return this.roleService.remove(+id)}getUsersByRole(id){return this.roleService.getUsersByRole(+id)}getRoleUsageStats(id){return this.roleService.getRoleUsageStats(+id)}canDeleteRole(id){return this.roleService.canDeleteRole(+id)}constructor(roleService,utilService){this.roleService=roleService;this.utilService=utilService}};_ts_decorate([(0,_permissiondecorator.Permission)("role:create"),(0,_common.Post)(),_ts_param(0,(0,_common.Body)()),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _createroledto.CreateRoleDto==="undefined"?Object:_createroledto.CreateRoleDto]),_ts_metadata("design:returntype",void 0)],RoleController.prototype,"create",null);_ts_decorate([(0,_permissiondecorator.Permission)("role:findAll"),(0,_common.Get)(),(0,_swagger.ApiQuery)({type:_filterdto.FilterDto}),_ts_param(0,(0,_common.Query)()),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[void 0]),_ts_metadata("design:returntype",void 0)],RoleController.prototype,"findAll",null);_ts_decorate([(0,_permissiondecorator.Permission)("role:findOne"),(0,_common.Get)(":id"),_ts_param(0,(0,_common.Param)("id")),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[String]),_ts_metadata("design:returntype",void 0)],RoleController.prototype,"findOne",null);_ts_decorate([(0,_permissiondecorator.Permission)("role:update"),(0,_common.Patch)(":id"),_ts_param(0,(0,_common.Param)("id")),_ts_param(1,(0,_common.Body)()),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[String,typeof _updateroledto.UpdateRoleDto==="undefined"?Object:_updateroledto.UpdateRoleDto]),_ts_metadata("design:returntype",void 0)],RoleController.prototype,"update",null);_ts_decorate([(0,_permissiondecorator.Permission)("role:remove"),(0,_common.Delete)(":id"),_ts_param(0,(0,_common.Param)("id")),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[String]),_ts_metadata("design:returntype",void 0)],RoleController.prototype,"remove",null);_ts_decorate([(0,_permissiondecorator.Permission)("role:findUsers"),(0,_common.Get)(":id/users"),(0,_swagger.ApiOperation)({summary:"Get all users assigned to this role"}),_ts_param(0,(0,_common.Param)("id")),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[String]),_ts_metadata("design:returntype",void 0)],RoleController.prototype,"getUsersByRole",null);_ts_decorate([(0,_permissiondecorator.Permission)("role:stats"),(0,_common.Get)(":id/stats"),(0,_swagger.ApiOperation)({summary:"Get role usage statistics"}),_ts_param(0,(0,_common.Param)("id")),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[String]),_ts_metadata("design:returntype",void 0)],RoleController.prototype,"getRoleUsageStats",null);_ts_decorate([(0,_permissiondecorator.Permission)("role:canDelete"),(0,_common.Get)(":id/can-delete"),(0,_swagger.ApiOperation)({summary:"Check if role can be safely deleted"}),_ts_param(0,(0,_common.Param)("id")),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[String]),_ts_metadata("design:returntype",void 0)],RoleController.prototype,"canDeleteRole",null);RoleController=_ts_decorate([(0,_swagger.ApiTags)("Role"),(0,_swagger.ApiBearerAuth)(),(0,_common.Controller)("role"),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _roleservice.RoleService==="undefined"?Object:_roleservice.RoleService,typeof _services.UtilService==="undefined"?Object:_services.UtilService])],RoleController);
//# sourceMappingURL=role.controller.js.map