# Authentication Integration Plan - CDS_QD Frontend

## 📋 Tóm tắt cuộc trò chuyện

### Quá trình khắc phục Multi-role System Backend
1. **Vấn đề ban đầu**: 
   - Admin login chỉ trả về role "user" thay vì "admin"
   - API `/role` bị 403 Forbidden với admin token

2. **Nguyên nhân gốc rễ**:
   - Entity `UsersRoleEntity` thiếu field `isPrimary: boolean`
   - Seed data gán sai role cho admin user (role_id: 2 thay vì 1)
   - Database schema không đồng bộ với entity changes

3. **Khắc phục triệt để**:
   - ✅ Thêm field `isPrimary: boolean` vào `UsersRoleEntity`
   - ✅ Sửa seed logic trong `user.seed.ts` và `role.seed.ts`
   - ✅ Reset database hoàn toàn và reseed data
   - ✅ Tạo thêm tài khoản test user/user với User role

### Kết quả đạt được
- ✅ **Admin account**: `admin/admin` → Admin role (id: 1) với full permissions
- ✅ **User account**: `user/user` → User role (id: 2) với limited permissions
- ✅ **Authorization**: Admin access APIs thành công, User bị 403 như mong đợi
- ✅ **Multi-role system**: Hoạt động hoàn hảo với backward compatibility

### Backend API Structure đã verified
```json
// Login Response Format
{
  "result": true,
  "message": "Success",
  "data": {
    "id": 2,
    "session": "JWT_TOKEN_HERE",
    "role": {
      "id": 1,
      "name": "Admin"
    },
    "roles": [
      {
        "id": 1,
        "name": "Admin"
      }
    ]
  }
}
```

**Authentication Mechanism**:
- Bearer JWT token trong field `session`
- Multi-role support với backward compatibility
- Role-based authorization với Permission decorator
- Token expiration handling

## 🔍 Phân tích tình trạng hiện tại Frontend

### Vấn đề cần tối ưu trong Authentication System

#### 1. **NextAuth Configuration Issues**
- ❌ **Wrong API URL**: `${process.env.API_URL}/login` thay vì `/auth/login`
- ❌ **Wrong request format**: Gửi `{email, password}` thay vì `{username, password}`
- ❌ **Không handle Backend response format**: Expect NextAuth format thay vì Backend format
- ❌ **Missing token storage**: Không lưu JWT token từ Backend

#### 2. **API Integration Problems**
- ❌ **Base URL mismatch**: `NEXT_PUBLIC_API_URL` chưa được set đúng port 8080
- ❌ **Token handling**: Auth interceptor lấy từ localStorage thay vì NextAuth session
- ❌ **Response format**: Custom instance expect ApiResponse nhưng Backend trả về khác format

#### 3. **UserDropdown Component Issues**
- ❌ **Data source**: Dùng NextAuth session thay vì Backend user profile
- ❌ **Missing role display**: Không hiển thị user role information
- ❌ **No profile API call**: Không gọi API để lấy user profile sau login

#### 4. **Session Management Problems**
- ❌ **Dual session**: NextAuth session + Backend JWT token không sync
- ❌ **No refresh logic**: Không handle token refresh từ Backend
- ❌ **Route protection**: Middleware dùng NextAuth token thay vì Backend authorization

## 🎯 Kế hoạch tích hợp chi tiết

### Phase 1: API Client Setup & Generation
**Mục tiêu**: Cấu hình đúng API client để connect với Backend

#### Task 1.1: Update Environment & Base URL
- [ ] Set `NEXT_PUBLIC_API_URL=http://localhost:8080` trong `.env.local`
- [ ] Verify Orval config pointing to correct Backend swagger
- [ ] Update custom-instance base URL configuration

#### Task 1.2: Generate API Client
- [ ] Chạy `pnpm generate:api` để generate latest API client
- [ ] Verify generated auth endpoints có đúng format
- [ ] Update TypeScript types cho Backend response format

#### Task 1.3: Fix API Response Types
- [ ] Update `src/api/types/index.ts` với Backend response format:
```typescript
export interface BackendApiResponse<T> {
  result: boolean;
  message: string;
  data: T;
}

export interface LoginResponse {
  id: number;
  session: string;
  expired?: string;
  refreshToken?: string;
  role: {
    id: number;
    name: string;
  };
  roles: Array<{
    id: number;
    name: string;
  }>;
}
```

### Phase 2: Authentication Flow Implementation
**Mục tiêu**: Thay thế NextAuth bằng Backend authentication

#### Task 2.1: Create Authentication Context
- [ ] Tạo `src/contexts/AuthContext.tsx` với:
  - Login/logout functions
  - User state management
  - Token storage & refresh logic
  - Role-based permissions

#### Task 2.2: Implement Login Flow
- [ ] Tạo custom login function gọi Backend `/auth/login`
- [ ] Handle Backend response format đúng chuẩn
- [ ] Store JWT token và user info securely
- [ ] Implement automatic token refresh

#### Task 2.3: Update Login Page
- [ ] Modify login form để gửi `{username, password}`
- [ ] Connect với Backend API thay vì NextAuth
- [ ] Handle login success/error states
- [ ] Redirect sau khi login thành công

### Phase 3: User Profile Integration
**Mục tiêu**: Gọi API profile và hiển thị user info trong UserDropdown

#### Task 3.1: Profile API Integration
- [ ] Identify Backend profile endpoint (có thể là `/user/profile` hoặc `/user/me`)
- [ ] Generate API client cho profile endpoint
- [ ] Implement profile fetch sau khi login

#### Task 3.2: Update UserDropdown Component
- [ ] Replace NextAuth session với Backend user data
- [ ] Display user role information
- [ ] Show user profile data từ Backend
- [ ] Add role-based UI elements

#### Task 3.3: Profile Management
- [ ] Implement profile update functionality
- [ ] Handle profile image upload
- [ ] Sync profile changes với authentication state

### Phase 4: Route Protection & Authorization
**Mục tiêu**: Implement role-based route protection

#### Task 4.1: Update Middleware
- [ ] Replace NextAuth token check với Backend JWT verification
- [ ] Implement role-based route protection
- [ ] Handle token expiration redirects

#### Task 4.2: Create Route Guards
- [ ] Tạo HOC/hooks cho role-based access control
- [ ] Implement permission-based component rendering
- [ ] Add loading states cho authentication checks

#### Task 4.3: Error Handling
- [ ] Update error interceptor cho 401/403 handling
- [ ] Implement automatic logout on token expiration
- [ ] Add proper error messages cho authorization failures

## 📝 Implementation Notes

### Critical Requirements
1. **Backward Compatibility**: Đảm bảo existing components vẫn hoạt động
2. **Security**: Secure token storage và transmission
3. **Performance**: Minimize API calls và optimize caching
4. **User Experience**: Smooth login/logout flow với proper loading states

### Testing Strategy
1. **Login Flow**: Test với cả admin/admin và user/user accounts
2. **Authorization**: Verify role-based access control
3. **Token Refresh**: Test automatic token renewal
4. **Error Handling**: Test network errors và token expiration

### Next Steps Priority
1. **Phase 1** - API Client Setup (Highest Priority)
2. **Phase 2** - Authentication Flow (High Priority)  
3. **Phase 3** - User Profile Integration (Medium Priority)
4. **Phase 4** - Route Protection (Medium Priority)

---

**Status**: Ready to implement Phase 1
**Last Updated**: Current conversation
**Backend Status**: ✅ Multi-role system working perfectly
**Frontend Status**: ❌ Needs complete authentication integration
