{"version": 3, "sources": ["../../../../src/database/typeorm/seeds/account.seed.ts"], "sourcesContent": ["import { DataSource } from 'typeorm';\nimport { Seeder, SeederFactoryManager } from 'typeorm-extension';\nimport { UserEntity } from '~/database/typeorm/entities/user.entity';\nimport { AccountEntity } from '../entities/account.entity';\n\nexport default class AccountSeeder implements Seeder {\n    public async run(dataSource: DataSource, factoryManager: SeederFactoryManager): Promise<any> {\n        console.log('AccountSeeder is running...');\n        const repository = dataSource.getRepository(AccountEntity);\n        // Create admin account\n        if (!(await repository.countBy({ username: 'admin' }))) {\n            await repository.insert([\n                {\n                    username: 'admin',\n                    password: '$2b$08$NurMoRDe0qIYY1SL8EQFT.WUTbCf8u2gk7imco2XFlapibRscC2v.',\n                    salt: '$2b$08$NurMoRDe0qIYY1SL8EQFT.',\n                    isActive: true,\n                },\n            ]);\n        }\n\n        // Create user test account (password: \"user\")\n        if (!(await repository.countBy({ username: 'user' }))) {\n            await repository.insert([\n                {\n                    username: 'user',\n                    password: '$2b$08$W7QK7P7R43qpdRP3DQVuMuQGygEYWhMH1ehjMljFjJdMtyCzqPo8u',\n                    salt: '$2b$08$W7QK7P7R43qpdRP3DQVuMu',\n                    isActive: true,\n                },\n            ]);\n        }\n\n        const accountFactory = factoryManager.get(AccountEntity);\n        const userFactory = factoryManager.get(UserEntity);\n        // save 1 factory generated entity, to the database\n        const account = await accountFactory.save();\n        await userFactory.save({ accountId: account.id });\n        console.log('AccountSeeder is done!');\n    }\n}\n"], "names": ["Account<PERSON><PERSON><PERSON>", "run", "dataSource", "factoryManager", "console", "log", "repository", "getRepository", "AccountEntity", "countBy", "username", "insert", "password", "salt", "isActive", "accountFactory", "get", "userFactory", "UserEntity", "account", "save", "accountId", "id"], "mappings": "qJAKqBA,2CAHM,wDACG,8BAEf,IAAA,AAAMA,cAAN,MAAMA,cACjB,MAAaC,IAAIC,UAAsB,CAAEC,cAAoC,CAAgB,CACzFC,QAAQC,GAAG,CAAC,+BACZ,MAAMC,WAAaJ,WAAWK,aAAa,CAACC,4BAAa,EAEzD,GAAI,CAAE,MAAMF,WAAWG,OAAO,CAAC,CAAEC,SAAU,OAAQ,GAAK,CACpD,MAAMJ,WAAWK,MAAM,CAAC,CACpB,CACID,SAAU,QACVE,SAAU,+DACVC,KAAM,gCACNC,SAAU,IACd,EACH,CACL,CAGA,GAAI,CAAE,MAAMR,WAAWG,OAAO,CAAC,CAAEC,SAAU,MAAO,GAAK,CACnD,MAAMJ,WAAWK,MAAM,CAAC,CACpB,CACID,SAAU,OACVE,SAAU,+DACVC,KAAM,gCACNC,SAAU,IACd,EACH,CACL,CAEA,MAAMC,eAAiBZ,eAAea,GAAG,CAACR,4BAAa,EACvD,MAAMS,YAAcd,eAAea,GAAG,CAACE,sBAAU,EAEjD,MAAMC,QAAU,MAAMJ,eAAeK,IAAI,EACzC,OAAMH,YAAYG,IAAI,CAAC,CAAEC,UAAWF,QAAQG,EAAE,AAAC,GAC/ClB,QAAQC,GAAG,CAAC,yBAChB,CACJ"}