import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { UserEntity } from '~/database/typeorm/entities/user.entity';

@Injectable()
export class UserRepository extends Repository<UserEntity> {
    constructor(private dataSource: DataSource) {
        super(UserEntity, dataSource.createEntityManager());
    }

    findOneUserWithAllRelationsById = (id: number) => {
        return this.findOne({
            where: { id: id },
            relations: ['userRoles', 'userRoles.role', 'avatar', 'account'],
        });
    };

    // Find user with all roles (only multiple roles now)
    findOneUserWithAllRolesById = (id: number) => {
        return this.findOne({
            where: { id: id },
            relations: ['userRoles', 'userRoles.role', 'avatar', 'account'],
        });
    };

    findOneWithRalations = ({ where, relations }: { where: any; relations: string[] }) => {
        const builder = this.createQueryBuilder('entity');
        if (where) {
            builder.where(where);
        }

        if (relations.length) {
            relations.forEach((relation) => {
                builder.leftJoinAndMapOne(`entity.${relation}`, `entity.${relation}`, relation, `${relation}.id = entity.${relation}Id`);
            });
        }

        return builder.getOne();
    };

    // Find user by account ID with all roles
    findOneUserWithRolesByAccountId = (accountId: number) => {
        return this.findOne({
            where: { accountId },
            relations: ['userRoles', 'userRoles.role'],
        });
    };

    // Get all roles for a user (only multiple roles now)
    async getAllUserRoles(userId: number) {
        const user = await this.findOne({
            where: { id: userId },
            relations: ['userRoles', 'userRoles.role'],
        });

        if (!user) return [];

        const roles = [];

        // Add multiple roles
        if (user.userRoles && user.userRoles.length > 0) {
            user.userRoles.forEach((userRole) => {
                if (userRole.role) {
                    roles.push(userRole.role);
                }
            });
        }

        return roles;
    }
}
