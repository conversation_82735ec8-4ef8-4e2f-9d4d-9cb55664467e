# Đ<PERSON> xuất cải tiến hệ thống Primary Role

## Tổng quan

Dựa trên phân tích cơ chế primary role hiệ<PERSON> tại, tôi đề xuất một roadmap cải tiến từng bước để tăng tính linh hoạt mà vẫn duy trì backward compatibility.

## Phase 1: Documentation và Standardization (<PERSON><PERSON> lậ<PERSON> tức)

### 1.1 Tạo Primary Role Helper Service

```typescript
// src/shared/services/primary-role.service.ts
@Injectable()
export class PrimaryRoleService {
    /**
     * Get primary role with explicit logic
     * Priority: Single Role > First Multiple Role > null
     */
    getPrimaryRole(user: UserEntity): RoleEntity | null {
        // 1. Single role has highest priority (backward compatibility)
        if (user.roleId && user.role) {
            return user.role;
        }
        
        // 2. First role in userRoles array
        if (user.userRoles && user.userRoles.length > 0) {
            const firstUserRole = user.userRoles[0];
            if (firstUserRole?.role) {
                return firstUserRole.role;
            }
        }
        
        // 3. No role found
        return null;
    }

    /**
     * Get primary role ID for headers and compatibility
     */
    getPrimaryRoleId(user: UserEntity): number | null {
        const primaryRole = this.getPrimaryRole(user);
        return primaryRole?.id || null;
    }

    /**
     * Check if user has specific role as primary
     */
    hasPrimaryRole(user: UserEntity, roleId: number): boolean {
        const primaryRole = this.getPrimaryRole(user);
        return primaryRole?.id === roleId;
    }
}
```

### 1.2 Cập nhật AuthService sử dụng PrimaryRoleService

```typescript
// src/modules/auth/auth.service.ts
constructor(
    // ... existing dependencies
    private readonly primaryRoleService: PrimaryRoleService
) {}

public async login(data: { username: string; password: string }) {
    // ... existing login logic
    
    const allRoles = await this.userRepository.getAllUserRoles(user.id);
    const primaryRole = this.primaryRoleService.getPrimaryRole(user);
    
    return {
        result: true,
        message: 'Login successfully',
        data: {
            // ... existing fields
            role: primaryRole,           // Explicit primary role
            roles: allRoles,            // All roles
            primaryRoleId: primaryRole?.id || null,  // Explicit primary role ID
        },
    };
}
```

### 1.3 Cập nhật PermissionGuard

```typescript
// src/common/guards/permission.guard.ts
constructor(
    // ... existing dependencies
    private readonly primaryRoleService: PrimaryRoleService
) {}

private async verifyPermission(data: { req: Request; permission: string; params: any }) {
    // ... existing logic
    
    const primaryRoleId = this.primaryRoleService.getPrimaryRoleId(user);
    
    // Set headers with explicit logic
    data.req.headers['_roleId'] = primaryRoleId?.toString() || '';
    data.req.headers['_primaryRoleId'] = primaryRoleId?.toString() || '';  // New explicit header
    data.req.headers['_allRoleIds'] = roleIds.join(',');
    
    return true;
}
```

## Phase 2: Enhanced Primary Role Management (Tương lai gần)

### 2.1 Thêm trường isPrimary vào users_roles

```sql
-- Migration: Add isPrimary column
ALTER TABLE users_roles ADD COLUMN is_primary BOOLEAN DEFAULT FALSE;

-- Ensure only one primary role per user
CREATE UNIQUE INDEX idx_users_roles_primary 
ON users_roles (user_id) 
WHERE is_primary = TRUE;
```

### 2.2 Cập nhật UsersRoleEntity

```typescript
// src/database/typeorm/entities/usersRole.entity.ts
@Entity({ name: 'users_roles' })
export class UsersRoleEntity {
    @PrimaryColumn({ name: 'user_id', type: 'bigint' })
    userId: number;

    @PrimaryColumn({ name: 'role_id', type: 'bigint' })
    roleId: number;

    @Column({ name: 'is_primary', type: 'boolean', default: false })
    isPrimary: boolean;  // New field

    // ... existing relations
}
```

### 2.3 Enhanced PrimaryRoleService

```typescript
export class PrimaryRoleService {
    /**
     * Enhanced primary role logic with isPrimary support
     */
    getPrimaryRole(user: UserEntity): RoleEntity | null {
        // 1. Single role has highest priority (backward compatibility)
        if (user.roleId && user.role) {
            return user.role;
        }
        
        // 2. Explicitly marked primary role in multiple roles
        if (user.userRoles && user.userRoles.length > 0) {
            const primaryUserRole = user.userRoles.find(ur => ur.isPrimary);
            if (primaryUserRole?.role) {
                return primaryUserRole.role;
            }
        }
        
        // 3. Fallback to first role (existing behavior)
        if (user.userRoles && user.userRoles.length > 0) {
            const firstUserRole = user.userRoles[0];
            if (firstUserRole?.role) {
                return firstUserRole.role;
            }
        }
        
        return null;
    }

    /**
     * Set primary role for user (only works with multiple roles)
     */
    async setPrimaryRole(userId: number, roleId: number): Promise<boolean> {
        const user = await this.userRepository.findOneUserWithAllRolesById(userId);
        
        // Cannot set primary if user has single role
        if (user.roleId) {
            throw new HttpException('Cannot set primary role when user has single role', 400);
        }
        
        // Check if user has this role
        const hasRole = user.userRoles?.some(ur => ur.role.id === roleId);
        if (!hasRole) {
            throw new HttpException('User does not have this role', 400);
        }
        
        // Remove existing primary flag
        await this.usersRoleRepository.update(
            { userId },
            { isPrimary: false }
        );
        
        // Set new primary role
        await this.usersRoleRepository.update(
            { userId, roleId },
            { isPrimary: true }
        );
        
        return true;
    }
}
```

### 2.4 API Endpoints cho Primary Role Management

```typescript
// src/modules/user/user.controller.ts

@Permission('user:setPrimaryRole')
@Post(':id/primary-role')
@ApiOperation({ summary: 'Set primary role for user (multiple roles only)' })
setPrimaryRole(
    @Param('id') id: string, 
    @Body() dto: { roleId: number }
) {
    return this.primaryRoleService.setPrimaryRole(+id, dto.roleId);
}

@Permission('user:getPrimaryRole')
@Get(':id/primary-role')
@ApiOperation({ summary: 'Get current primary role for user' })
async getPrimaryRole(@Param('id') id: string) {
    const user = await this.userService.findOne(+id);
    const primaryRole = this.primaryRoleService.getPrimaryRole(user);
    
    return {
        primaryRole,
        canChangePrimary: !user.roleId,  // Can only change if no single role
        availableRoles: user.userRoles?.map(ur => ur.role) || []
    };
}
```

## Phase 3: Advanced Features (Tương lai xa)

### 3.1 Role Priority System

```typescript
// Add priority field to roles table
ALTER TABLE roles ADD COLUMN priority INTEGER DEFAULT 0;

// Enhanced fallback logic
getPrimaryRoleWithPriority(user: UserEntity): RoleEntity | null {
    // ... existing logic for single role and isPrimary
    
    // Fallback to highest priority role
    if (user.userRoles && user.userRoles.length > 0) {
        const sortedRoles = user.userRoles
            .map(ur => ur.role)
            .filter(role => role)
            .sort((a, b) => (b.priority || 0) - (a.priority || 0));
        
        return sortedRoles[0] || null;
    }
    
    return null;
}
```

### 3.2 User Preference System

```typescript
// User can set preferred primary role
@Column({ name: 'preferred_primary_role_id', type: 'int', nullable: true })
preferredPrimaryRoleId: number;

// Enhanced logic considering user preference
getPrimaryRoleWithPreference(user: UserEntity): RoleEntity | null {
    // ... existing logic
    
    // Consider user preference
    if (user.preferredPrimaryRoleId && user.userRoles) {
        const preferredRole = user.userRoles.find(
            ur => ur.role.id === user.preferredPrimaryRoleId
        );
        if (preferredRole?.role) {
            return preferredRole.role;
        }
    }
    
    // ... fallback logic
}
```

## Migration Strategy

### Step 1: Immediate (Phase 1)
1. ✅ Implement PrimaryRoleService
2. ✅ Update AuthService và PermissionGuard
3. ✅ Add comprehensive documentation
4. ✅ Add unit tests

### Step 2: Short term (Phase 2)
1. 🔄 Add isPrimary column migration
2. 🔄 Update entities và repositories
3. 🔄 Add primary role management APIs
4. 🔄 Update frontend to support primary role selection

### Step 3: Long term (Phase 3)
1. ⏳ Implement role priority system
2. ⏳ Add user preference system
3. ⏳ Advanced role management features

## Benefits

### Immediate Benefits (Phase 1)
- ✅ Explicit và documented primary role logic
- ✅ Centralized primary role management
- ✅ Better testability
- ✅ Consistent behavior across services

### Future Benefits (Phase 2-3)
- 🚀 User control over primary role
- 🚀 Flexible role management
- 🚀 Advanced role priority system
- 🚀 Better user experience

## Backward Compatibility

Tất cả phases đều duy trì 100% backward compatibility:
- Single role (`roleId`) luôn có priority cao nhất
- Existing API responses không thay đổi
- Frontend hiện tại tiếp tục hoạt động bình thường
- Database schema changes là additive only

## Conclusion

Đề xuất này cung cấp một roadmap rõ ràng để cải tiến hệ thống primary role từ đơn giản đến phức tạp, đảm bảo:

1. **Immediate value**: Documentation và standardization
2. **Future flexibility**: Enhanced primary role management
3. **Backward compatibility**: Không breaking changes
4. **Gradual adoption**: Có thể implement từng phase độc lập
