"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"default",{enumerable:true,get:function(){return UserSeeder}});const _accountentity=require("../entities/account.entity");const _roleentity=require("../entities/role.entity");const _userentity=require("../entities/user.entity");const _usersRoleentity=require("../entities/usersRole.entity");let UserSeeder=class UserSeeder{async run(dataSource,factoryManager){console.log("UserSeeder is running...");const repository=dataSource.getRepository(_userentity.UserEntity);const accountRepo=dataSource.getRepository(_accountentity.AccountEntity);const roleRepo=dataSource.getRepository(_roleentity.RoleEntity);const usersRoleRepo=dataSource.getRepository(_usersRoleentity.UsersRoleEntity);const account=await accountRepo.findOneBy({username:"admin"});const adminRole=await roleRepo.findOneBy({name:"Admin"});const existingUser=await repository.findOneBy({accountId:account?.id});if(!existingUser&&account&&adminRole){const user=await repository.save({accountId:account.id,hoTen:"Admin",email:"<EMAIL>"});await usersRoleRepo.save({userId:user.id,roleId:adminRole.id,isPrimary:true});console.log("Admin user created with multiple roles system")}console.log("UserSeeder is done!")}};
//# sourceMappingURL=user.seed.js.map