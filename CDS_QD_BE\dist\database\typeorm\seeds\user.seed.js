"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"default",{enumerable:true,get:function(){return UserSeeder}});const _accountentity=require("../entities/account.entity");const _roleentity=require("../entities/role.entity");const _userentity=require("../entities/user.entity");const _usersRoleentity=require("../entities/usersRole.entity");let UserSeeder=class UserSeeder{async run(dataSource,factoryManager){console.log("UserSeeder is running...");const repository=dataSource.getRepository(_userentity.UserEntity);const accountRepo=dataSource.getRepository(_accountentity.AccountEntity);const roleRepo=dataSource.getRepository(_roleentity.RoleEntity);const usersRoleRepo=dataSource.getRepository(_usersRoleentity.UsersRoleEntity);const adminAccount=await accountRepo.findOneBy({username:"admin"});const userAccount=await accountRepo.findOneBy({username:"user"});const adminRole=await roleRepo.findOneBy({name:"Admin"});const userRole=await roleRepo.findOneBy({name:"User"});console.log("Debug - Admin account found:",adminAccount?`ID: ${adminAccount.id}`:"NOT FOUND");console.log("Debug - User account found:",userAccount?`ID: ${userAccount.id}`:"NOT FOUND");console.log("Debug - Admin role found:",adminRole?`ID: ${adminRole.id}, Name: ${adminRole.name}`:"NOT FOUND");console.log("Debug - User role found:",userRole?`ID: ${userRole.id}, Name: ${userRole.name}`:"NOT FOUND");const existingAdminUser=await repository.findOneBy({accountId:adminAccount?.id});console.log("Debug - Existing admin user:",existingAdminUser?`ID: ${existingAdminUser.id}`:"NOT FOUND");if(!existingAdminUser&&adminAccount&&adminRole){const adminUser=await repository.save({accountId:adminAccount.id,hoTen:"Admin",email:"<EMAIL>"});console.log("Debug - Admin user created:",`ID: ${adminUser.id}, AccountID: ${adminUser.accountId}`);const adminUserRole=await usersRoleRepo.save({userId:adminUser.id,roleId:adminRole.id,isPrimary:true});console.log("Debug - Admin UserRole created:",`UserID: ${adminUserRole.userId}, RoleID: ${adminUserRole.roleId}, IsPrimary: ${adminUserRole.isPrimary}`);console.log("Admin user created with multiple roles system")}else{console.log("Admin user creation skipped - User already exists or missing dependencies")}const existingTestUser=await repository.findOneBy({accountId:userAccount?.id});console.log("Debug - Existing test user:",existingTestUser?`ID: ${existingTestUser.id}`:"NOT FOUND");if(!existingTestUser&&userAccount&&userRole){const testUser=await repository.save({accountId:userAccount.id,hoTen:"Test User",email:"<EMAIL>"});console.log("Debug - Test user created:",`ID: ${testUser.id}, AccountID: ${testUser.accountId}`);const testUserRole=await usersRoleRepo.save({userId:testUser.id,roleId:userRole.id,isPrimary:true});console.log("Debug - Test UserRole created:",`UserID: ${testUserRole.userId}, RoleID: ${testUserRole.roleId}, IsPrimary: ${testUserRole.isPrimary}`);console.log("Test user created with User role")}else{console.log("Test user creation skipped - User already exists or missing dependencies")}console.log("UserSeeder is done!")}};
//# sourceMappingURL=user.seed.js.map