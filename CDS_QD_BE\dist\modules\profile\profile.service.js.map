{"version": 3, "sources": ["../../../src/modules/profile/profile.service.ts"], "sourcesContent": ["import { BadRequestException, Injectable } from '@nestjs/common';\nimport { AccountRepository } from '~/database/typeorm/repositories/account.repository';\nimport { UserRepository } from '~/database/typeorm/repositories/user.repository';\nimport { ChangePasswordDto } from '~/modules/profile/dto/changePassword.dto';\nimport { TokenService } from '~/shared/services';\nimport { UpdateProfileDto } from './dto/update-profile.dto';\n\n@Injectable()\nexport class ProfileService {\n    constructor(\n        private readonly userRepository: UserRepository,\n        private readonly accountRepository: AccountRepository,\n        private readonly tokenService: TokenService,\n    ) {}\n\n    findOne(id: number) {\n        const builder = this.userRepository.createQueryBuilder('user');\n        builder.leftJoinAndSelect('user.userRoles', 'roles');\n        builder.leftJoinAndSelect('roles.role', 'role');\n        builder.leftJoinAndSelect('role.permissions', 'permission');\n        builder.leftJoinAndSelect('user.avatar', 'avatar');\n        builder.where('user.id = :id', { id });\n        return builder.getOne();\n    }\n\n    update(id: number, updateProfileDto: UpdateProfileDto) {\n        return this.userRepository.update(id, updateProfileDto);\n    }\n\n    async changePassword(id: number, updateProfileDto: ChangePasswordDto) {\n        if (updateProfileDto.new_password !== updateProfileDto.confirm_password) {\n            throw new BadRequestException('Mật khẩu mới không khớp');\n        }\n\n        const user = await this.userRepository.findOneBy({ id });\n        if (!user) {\n            throw new BadRequestException('Không tìm thấy tài khoản');\n        }\n\n        const account = await this.accountRepository.findOneBy({ id: user.accountId });\n        const isMatch = await this.tokenService.isPasswordCorrect(updateProfileDto.old_password, account.password);\n        if (!isMatch) {\n            throw new BadRequestException('Mật khẩu cũ không đúng');\n        }\n\n        const { salt, hash } = this.tokenService.hashPassword(updateProfileDto.new_password);\n        const res = await this.accountRepository.update(\n            { id: user.accountId },\n            {\n                password: hash,\n                salt,\n            },\n        );\n\n        return res;\n    }\n}\n"], "names": ["ProfileService", "findOne", "id", "builder", "userRepository", "createQueryBuilder", "leftJoinAndSelect", "where", "getOne", "update", "updateProfileDto", "changePassword", "new_password", "confirm_password", "BadRequestException", "user", "findOneBy", "account", "accountRepository", "accountId", "isMatch", "tokenService", "isPasswordCorrect", "old_password", "password", "salt", "hash", "hashPassword", "res", "constructor"], "mappings": "oGAQaA,wDAAAA,wCARmC,mDACd,wFACH,+EAEF,skBAItB,IAAA,AAAMA,eAAN,MAAMA,eAOTC,QAAQC,EAAU,CAAE,CAChB,MAAMC,QAAU,IAAI,CAACC,cAAc,CAACC,kBAAkB,CAAC,QACvDF,QAAQG,iBAAiB,CAAC,iBAAkB,SAC5CH,QAAQG,iBAAiB,CAAC,aAAc,QACxCH,QAAQG,iBAAiB,CAAC,mBAAoB,cAC9CH,QAAQG,iBAAiB,CAAC,cAAe,UACzCH,QAAQI,KAAK,CAAC,gBAAiB,CAAEL,EAAG,GACpC,OAAOC,QAAQK,MAAM,EACzB,CAEAC,OAAOP,EAAU,CAAEQ,gBAAkC,CAAE,CACnD,OAAO,IAAI,CAACN,cAAc,CAACK,MAAM,CAACP,GAAIQ,iBAC1C,CAEA,MAAMC,eAAeT,EAAU,CAAEQ,gBAAmC,CAAE,CAClE,GAAIA,iBAAiBE,YAAY,GAAKF,iBAAiBG,gBAAgB,CAAE,CACrE,MAAM,IAAIC,2BAAmB,CAAC,0BAClC,CAEA,MAAMC,KAAO,MAAM,IAAI,CAACX,cAAc,CAACY,SAAS,CAAC,CAAEd,EAAG,GACtD,GAAI,CAACa,KAAM,CACP,MAAM,IAAID,2BAAmB,CAAC,2BAClC,CAEA,MAAMG,QAAU,MAAM,IAAI,CAACC,iBAAiB,CAACF,SAAS,CAAC,CAAEd,GAAIa,KAAKI,SAAS,AAAC,GAC5E,MAAMC,QAAU,MAAM,IAAI,CAACC,YAAY,CAACC,iBAAiB,CAACZ,iBAAiBa,YAAY,CAAEN,QAAQO,QAAQ,EACzG,GAAI,CAACJ,QAAS,CACV,MAAM,IAAIN,2BAAmB,CAAC,yBAClC,CAEA,KAAM,CAAEW,IAAI,CAAEC,IAAI,CAAE,CAAG,IAAI,CAACL,YAAY,CAACM,YAAY,CAACjB,iBAAiBE,YAAY,EACnF,MAAMgB,IAAM,MAAM,IAAI,CAACV,iBAAiB,CAACT,MAAM,CAC3C,CAAEP,GAAIa,KAAKI,SAAS,AAAC,EACrB,CACIK,SAAUE,KACVD,IACJ,GAGJ,OAAOG,GACX,CA9CAC,YACI,AAAiBzB,cAA8B,CAC/C,AAAiBc,iBAAoC,CACrD,AAAiBG,YAA0B,CAC7C,MAHmBjB,eAAAA,oBACAc,kBAAAA,uBACAG,aAAAA,YAClB,CA2CP"}