{"version": 3, "sources": ["../../../src/modules/role/role.controller.ts"], "sourcesContent": ["import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common';\nimport { ApiBearerAuth, ApiQ<PERSON>y, ApiT<PERSON>s, ApiOperation } from '@nestjs/swagger';\nimport { Permission } from '~/common/decorators/permission.decorator';\nimport { FilterDto } from '~/common/dtos/filter.dto';\nimport { UtilService } from '~/shared/services';\nimport { CreateRoleDto } from './dto/create-role.dto';\nimport { UpdateRoleDto } from './dto/update-role.dto';\nimport { RoleService } from './role.service';\n\n@ApiTags('Role')\n@ApiBearerAuth()\n@Controller('role')\nexport class RoleController {\n    constructor(private readonly roleService: RoleService, private readonly utilService: UtilService) {}\n\n    @Permission('role:create')\n    @Post()\n    create(@Body() createRoleDto: CreateRoleDto) {\n        return this.roleService.create(createRoleDto);\n    }\n\n    @Permission('role:findAll')\n    @Get()\n    @ApiQuery({ type: FilterDto })\n    findAll(@Query() queries) {\n        return this.roleService.findAll({ ...queries });\n    }\n\n    @Permission('role:findOne')\n    @Get(':id')\n    findOne(@Param('id') id: string) {\n        return this.roleService.findOne(+id);\n    }\n\n    @Permission('role:update')\n    @Patch(':id')\n    update(@Param('id') id: string, @Body() updateRoleDto: UpdateRoleDto) {\n        return this.roleService.update(+id, updateRoleDto);\n    }\n\n    @Permission('role:remove')\n    @Delete(':id')\n    remove(@Param('id') id: string) {\n        return this.roleService.remove(+id);\n    }\n\n    // Multiple users management endpoints\n    @Permission('role:findUsers')\n    @Get(':id/users')\n    @ApiOperation({ summary: 'Get all users assigned to this role' })\n    getUsersByRole(@Param('id') id: string) {\n        return this.roleService.getUsersByRole(+id);\n    }\n\n    @Permission('role:stats')\n    @Get(':id/stats')\n    @ApiOperation({ summary: 'Get role usage statistics' })\n    getRoleUsageStats(@Param('id') id: string) {\n        return this.roleService.getRoleUsageStats(+id);\n    }\n\n    @Permission('role:canDelete')\n    @Get(':id/can-delete')\n    @ApiOperation({ summary: 'Check if role can be safely deleted' })\n    canDeleteRole(@Param('id') id: string) {\n        return this.roleService.canDeleteRole(+id);\n    }\n}\n"], "names": ["RoleController", "create", "createRoleDto", "roleService", "findAll", "queries", "findOne", "id", "update", "updateRoleDto", "remove", "getUsersByRole", "getRoleUsageStats", "canDeleteRole", "constructor", "utilService", "type", "Filter<PERSON><PERSON>", "summary"], "mappings": "oGAYaA,wDAAAA,wCAZ4D,yCACV,sDACpC,yEACD,wDACE,sDACE,sDACA,oDACF,sqBAKrB,IAAA,AAAMA,eAAN,MAAMA,eAGT,AAEAC,OAAO,AAAQC,aAA4B,CAAE,CACzC,OAAO,IAAI,CAACC,WAAW,CAACF,MAAM,CAACC,cACnC,CAEA,AAGAE,QAAQ,AAASC,OAAO,CAAE,CACtB,OAAO,IAAI,CAACF,WAAW,CAACC,OAAO,CAAC,CAAE,GAAGC,OAAO,AAAC,EACjD,CAEA,AAEAC,QAAQ,AAAaC,EAAU,CAAE,CAC7B,OAAO,IAAI,CAACJ,WAAW,CAACG,OAAO,CAAC,CAACC,GACrC,CAEA,AAEAC,OAAO,AAAaD,EAAU,CAAE,AAAQE,aAA4B,CAAE,CAClE,OAAO,IAAI,CAACN,WAAW,CAACK,MAAM,CAAC,CAACD,GAAIE,cACxC,CAEA,AAEAC,OAAO,AAAaH,EAAU,CAAE,CAC5B,OAAO,IAAI,CAACJ,WAAW,CAACO,MAAM,CAAC,CAACH,GACpC,CAGA,AAGAI,eAAe,AAAaJ,EAAU,CAAE,CACpC,OAAO,IAAI,CAACJ,WAAW,CAACQ,cAAc,CAAC,CAACJ,GAC5C,CAEA,AAGAK,kBAAkB,AAAaL,EAAU,CAAE,CACvC,OAAO,IAAI,CAACJ,WAAW,CAACS,iBAAiB,CAAC,CAACL,GAC/C,CAEA,AAGAM,cAAc,AAAaN,EAAU,CAAE,CACnC,OAAO,IAAI,CAACJ,WAAW,CAACU,aAAa,CAAC,CAACN,GAC3C,CArDAO,YAAY,AAAiBX,WAAwB,CAAE,AAAiBY,WAAwB,CAAE,MAArEZ,YAAAA,iBAA2CY,YAAAA,WAA2B,CAsDvG,kdA5CgBC,KAAMC,oBAAS,6xCA0BXC,QAAS,+WAOTA,QAAS,iXAOTA,QAAS"}