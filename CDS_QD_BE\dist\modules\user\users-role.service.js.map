{"version": 3, "sources": ["../../../src/modules/user/users-role.service.ts"], "sourcesContent": ["import { Injectable, HttpException } from '@nestjs/common';\nimport { DatabaseService } from '~/database/typeorm/database.service';\n\n@Injectable()\nexport class UsersRoleService {\n    constructor(private readonly database: DatabaseService) {}\n\n    // Assign multiple roles to a user\n    async assignRolesToUser(userId: number, roleIds: number[]) {\n        // Validate user exists\n        const user = await this.database.user.findOneBy({ id: userId });\n        if (!user) {\n            throw new HttpException('Không tìm thấy người dùng', 404);\n        }\n\n        // Validate roles exist\n        const roles = await this.database.role.findByIds(roleIds);\n        if (roles.length !== roleIds.length) {\n            throw new HttpException('Một hoặc nhiều role không tồn tại', 400);\n        }\n\n        // Remove existing user roles to avoid duplicates\n        await this.database.usersRole.delete({ userId });\n\n        // Create new user-role relationships\n        const userRoles = roleIds.map(roleId => ({\n            userId,\n            roleId\n        }));\n\n        return this.database.usersRole.save(userRoles);\n    }\n\n    // Add a single role to user (without removing existing ones)\n    async addRoleToUser(userId: number, roleId: number) {\n        // Check if user exists\n        const user = await this.database.user.findOneBy({ id: userId });\n        if (!user) {\n            throw new HttpException('Không tìm thấy người dùng', 404);\n        }\n\n        // Check if role exists\n        const role = await this.database.role.findOneBy({ id: roleId });\n        if (!role) {\n            throw new HttpException('Không tìm thấy role', 404);\n        }\n\n        // Check if user already has this role\n        const existingUserRole = await this.database.usersRole.findOne({\n            where: { userId, roleId }\n        });\n\n        if (existingUserRole) {\n            throw new HttpException('Người dùng đã có role này', 400);\n        }\n\n        // Create new user-role relationship\n        return this.database.usersRole.save({ userId, roleId });\n    }\n\n    // Remove a specific role from user\n    async removeRoleFromUser(userId: number, roleId: number) {\n        const result = await this.database.usersRole.delete({ userId, roleId });\n        \n        if (result.affected === 0) {\n            throw new HttpException('Không tìm thấy quan hệ user-role để xóa', 404);\n        }\n\n        return result;\n    }\n\n    // Get all roles for a user\n    async getUserRoles(userId: number) {\n        return this.database.usersRole.find({\n            where: { userId },\n            relations: ['role']\n        });\n    }\n\n    // Get all users for a role\n    async getRoleUsers(roleId: number) {\n        return this.database.usersRole.find({\n            where: { roleId },\n            relations: ['user']\n        });\n    }\n\n    // Check if user has a specific role\n    async userHasRole(userId: number, roleId: number): Promise<boolean> {\n        const userRole = await this.database.usersRole.findOne({\n            where: { userId, roleId }\n        });\n        return !!userRole;\n    }\n\n    // Bulk assign roles to multiple users\n    async bulkAssignRoles(userIds: number[], roleIds: number[]) {\n        const userRoles = [];\n        \n        for (const userId of userIds) {\n            for (const roleId of roleIds) {\n                userRoles.push({ userId, roleId });\n            }\n        }\n\n        // Remove existing relationships for these users\n        await this.database.usersRole.delete({ userId: { $in: userIds } as any });\n\n        return this.database.usersRole.save(userRoles);\n    }\n\n    // Remove all roles from a user\n    async removeAllUserRoles(userId: number) {\n        return this.database.usersRole.delete({ userId });\n    }\n\n    // Remove all users from a role\n    async removeAllRoleUsers(roleId: number) {\n        return this.database.usersRole.delete({ roleId });\n    }\n}\n"], "names": ["UsersRoleService", "assignRolesToUser", "userId", "roleIds", "user", "database", "findOneBy", "id", "HttpException", "roles", "role", "findByIds", "length", "usersRole", "delete", "userRoles", "map", "roleId", "save", "addRoleToUser", "existingUserRole", "findOne", "where", "removeRoleFromUser", "result", "affected", "getUserRoles", "find", "relations", "getRoleUsers", "userHasRole", "userRole", "bulkAssignRoles", "userIds", "push", "$in", "removeAllUserRoles", "removeAllRoleUsers", "constructor"], "mappings": "oGAIaA,0DAAAA,0CAJ6B,iDACV,wlBAGzB,IAAA,AAAMA,iBAAN,MAAMA,iBAIT,MAAMC,kBAAkBC,MAAc,CAAEC,OAAiB,CAAE,CAEvD,MAAMC,KAAO,MAAM,IAAI,CAACC,QAAQ,CAACD,IAAI,CAACE,SAAS,CAAC,CAAEC,GAAIL,MAAO,GAC7D,GAAI,CAACE,KAAM,CACP,MAAM,IAAII,qBAAa,CAAC,4BAA6B,IACzD,CAGA,MAAMC,MAAQ,MAAM,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAACC,SAAS,CAACR,SACjD,GAAIM,MAAMG,MAAM,GAAKT,QAAQS,MAAM,CAAE,CACjC,MAAM,IAAIJ,qBAAa,CAAC,oCAAqC,IACjE,CAGA,MAAM,IAAI,CAACH,QAAQ,CAACQ,SAAS,CAACC,MAAM,CAAC,CAAEZ,MAAO,GAG9C,MAAMa,UAAYZ,QAAQa,GAAG,CAACC,QAAW,CAAA,CACrCf,OACAe,MACJ,CAAA,GAEA,OAAO,IAAI,CAACZ,QAAQ,CAACQ,SAAS,CAACK,IAAI,CAACH,UACxC,CAGA,MAAMI,cAAcjB,MAAc,CAAEe,MAAc,CAAE,CAEhD,MAAMb,KAAO,MAAM,IAAI,CAACC,QAAQ,CAACD,IAAI,CAACE,SAAS,CAAC,CAAEC,GAAIL,MAAO,GAC7D,GAAI,CAACE,KAAM,CACP,MAAM,IAAII,qBAAa,CAAC,4BAA6B,IACzD,CAGA,MAAME,KAAO,MAAM,IAAI,CAACL,QAAQ,CAACK,IAAI,CAACJ,SAAS,CAAC,CAAEC,GAAIU,MAAO,GAC7D,GAAI,CAACP,KAAM,CACP,MAAM,IAAIF,qBAAa,CAAC,sBAAuB,IACnD,CAGA,MAAMY,iBAAmB,MAAM,IAAI,CAACf,QAAQ,CAACQ,SAAS,CAACQ,OAAO,CAAC,CAC3DC,MAAO,CAAEpB,OAAQe,MAAO,CAC5B,GAEA,GAAIG,iBAAkB,CAClB,MAAM,IAAIZ,qBAAa,CAAC,4BAA6B,IACzD,CAGA,OAAO,IAAI,CAACH,QAAQ,CAACQ,SAAS,CAACK,IAAI,CAAC,CAAEhB,OAAQe,MAAO,EACzD,CAGA,MAAMM,mBAAmBrB,MAAc,CAAEe,MAAc,CAAE,CACrD,MAAMO,OAAS,MAAM,IAAI,CAACnB,QAAQ,CAACQ,SAAS,CAACC,MAAM,CAAC,CAAEZ,OAAQe,MAAO,GAErE,GAAIO,OAAOC,QAAQ,GAAK,EAAG,CACvB,MAAM,IAAIjB,qBAAa,CAAC,0CAA2C,IACvE,CAEA,OAAOgB,MACX,CAGA,MAAME,aAAaxB,MAAc,CAAE,CAC/B,OAAO,IAAI,CAACG,QAAQ,CAACQ,SAAS,CAACc,IAAI,CAAC,CAChCL,MAAO,CAAEpB,MAAO,EAChB0B,UAAW,CAAC,OAAO,AACvB,EACJ,CAGA,MAAMC,aAAaZ,MAAc,CAAE,CAC/B,OAAO,IAAI,CAACZ,QAAQ,CAACQ,SAAS,CAACc,IAAI,CAAC,CAChCL,MAAO,CAAEL,MAAO,EAChBW,UAAW,CAAC,OAAO,AACvB,EACJ,CAGA,MAAME,YAAY5B,MAAc,CAAEe,MAAc,CAAoB,CAChE,MAAMc,SAAW,MAAM,IAAI,CAAC1B,QAAQ,CAACQ,SAAS,CAACQ,OAAO,CAAC,CACnDC,MAAO,CAAEpB,OAAQe,MAAO,CAC5B,GACA,MAAO,CAAC,CAACc,QACb,CAGA,MAAMC,gBAAgBC,OAAiB,CAAE9B,OAAiB,CAAE,CACxD,MAAMY,UAAY,EAAE,CAEpB,IAAK,MAAMb,UAAU+B,QAAS,CAC1B,IAAK,MAAMhB,UAAUd,QAAS,CAC1BY,UAAUmB,IAAI,CAAC,CAAEhC,OAAQe,MAAO,EACpC,CACJ,CAGA,MAAM,IAAI,CAACZ,QAAQ,CAACQ,SAAS,CAACC,MAAM,CAAC,CAAEZ,OAAQ,CAAEiC,IAAKF,OAAQ,CAAS,GAEvE,OAAO,IAAI,CAAC5B,QAAQ,CAACQ,SAAS,CAACK,IAAI,CAACH,UACxC,CAGA,MAAMqB,mBAAmBlC,MAAc,CAAE,CACrC,OAAO,IAAI,CAACG,QAAQ,CAACQ,SAAS,CAACC,MAAM,CAAC,CAAEZ,MAAO,EACnD,CAGA,MAAMmC,mBAAmBpB,MAAc,CAAE,CACrC,OAAO,IAAI,CAACZ,QAAQ,CAACQ,SAAS,CAACC,MAAM,CAAC,CAAEG,MAAO,EACnD,CAlHAqB,YAAY,AAAiBjC,QAAyB,CAAE,MAA3BA,SAAAA,QAA4B,CAmH7D"}