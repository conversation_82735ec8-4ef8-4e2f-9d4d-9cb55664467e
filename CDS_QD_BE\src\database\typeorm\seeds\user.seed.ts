import { DataSource } from 'typeorm';
import { Seeder, SeederFactoryManager } from 'typeorm-extension';
import { AccountEntity } from '~/database/typeorm/entities/account.entity';
import { RoleEntity } from '~/database/typeorm/entities/role.entity';
import { UserEntity } from '~/database/typeorm/entities/user.entity';
import { UsersRoleEntity } from '~/database/typeorm/entities/usersRole.entity';

export default class UserSeeder implements Seeder {
    public async run(dataSource: DataSource, factoryManager: SeederFactoryManager): Promise<any> {
        console.log('UserSeeder is running...');
        const repository = dataSource.getRepository(UserEntity);
        const accountRepo = dataSource.getRepository(AccountEntity);
        const roleRepo = dataSource.getRepository(RoleEntity);
        const usersRoleRepo = dataSource.getRepository(UsersRoleEntity);

        const adminAccount = await accountRepo.findOneBy({ username: 'admin' });
        const userAccount = await accountRepo.findOneBy({ username: 'user' });
        const adminRole = await roleRepo.findOneBy({ name: 'Admin' });
        const userRole = await roleRepo.findOneBy({ name: 'User' });

        console.log('Debug - Admin account found:', adminAccount ? `ID: ${adminAccount.id}` : 'NOT FOUND');
        console.log('Debug - User account found:', userAccount ? `ID: ${userAccount.id}` : 'NOT FOUND');
        console.log('Debug - Admin role found:', adminRole ? `ID: ${adminRole.id}, Name: ${adminRole.name}` : 'NOT FOUND');
        console.log('Debug - User role found:', userRole ? `ID: ${userRole.id}, Name: ${userRole.name}` : 'NOT FOUND');

        // Create admin user
        const existingAdminUser = await repository.findOneBy({ accountId: adminAccount?.id });
        console.log('Debug - Existing admin user:', existingAdminUser ? `ID: ${existingAdminUser.id}` : 'NOT FOUND');

        if (!existingAdminUser && adminAccount && adminRole) {
            // Create admin user without single role
            const adminUser = await repository.save({
                accountId: adminAccount.id,
                hoTen: 'Admin',
                email: '<EMAIL>',
            });

            console.log('Debug - Admin user created:', `ID: ${adminUser.id}, AccountID: ${adminUser.accountId}`);

            // Assign admin role through users_roles table
            const adminUserRole = await usersRoleRepo.save({
                userId: adminUser.id,
                roleId: adminRole.id,
                isPrimary: true,
            });

            console.log(
                'Debug - Admin UserRole created:',
                `UserID: ${adminUserRole.userId}, RoleID: ${adminUserRole.roleId}, IsPrimary: ${adminUserRole.isPrimary}`,
            );
            console.log('Admin user created with multiple roles system');
        } else {
            console.log('Admin user creation skipped - User already exists or missing dependencies');
        }

        // Create test user
        const existingTestUser = await repository.findOneBy({ accountId: userAccount?.id });
        console.log('Debug - Existing test user:', existingTestUser ? `ID: ${existingTestUser.id}` : 'NOT FOUND');

        if (!existingTestUser && userAccount && userRole) {
            // Create test user
            const testUser = await repository.save({
                accountId: userAccount.id,
                hoTen: 'Test User',
                email: '<EMAIL>',
            });

            console.log('Debug - Test user created:', `ID: ${testUser.id}, AccountID: ${testUser.accountId}`);

            // Assign user role through users_roles table
            const testUserRole = await usersRoleRepo.save({
                userId: testUser.id,
                roleId: userRole.id,
                isPrimary: true,
            });

            console.log(
                'Debug - Test UserRole created:',
                `UserID: ${testUserRole.userId}, RoleID: ${testUserRole.roleId}, IsPrimary: ${testUserRole.isPrimary}`,
            );
            console.log('Test user created with User role');
        } else {
            console.log('Test user creation skipped - User already exists or missing dependencies');
        }

        console.log('UserSeeder is done!');
    }
}
