import { DataSource } from 'typeorm';
import { Seeder, SeederFactoryManager } from 'typeorm-extension';
import { AccountEntity } from '~/database/typeorm/entities/account.entity';
import { RoleEntity } from '~/database/typeorm/entities/role.entity';
import { UserEntity } from '~/database/typeorm/entities/user.entity';
import { UsersRoleEntity } from '~/database/typeorm/entities/usersRole.entity';

export default class UserSeeder implements Seeder {
    public async run(dataSource: DataSource, factoryManager: SeederFactoryManager): Promise<any> {
        console.log('UserSeeder is running...');
        const repository = dataSource.getRepository(UserEntity);
        const accountRepo = dataSource.getRepository(AccountEntity);
        const roleRepo = dataSource.getRepository(RoleEntity);
        const usersRoleRepo = dataSource.getRepository(UsersRoleEntity);

        const account = await accountRepo.findOneBy({ username: 'admin' });
        const adminRole = await roleRepo.findOneBy({ name: 'Admin' });

        // Check if admin user already exists
        const existingUser = await repository.findOneBy({ accountId: account?.id });

        if (!existingUser && account && adminRole) {
            // Create admin user without single role
            const user = await repository.save({
                accountId: account.id,
                hoTen: 'Admin',
                email: '<EMAIL>',
            });

            // Assign admin role through users_roles table
            await usersRoleRepo.save({
                userId: user.id,
                roleId: adminRole.id,
                isPrimary: true,
            });

            console.log('Admin user created with multiple roles system');
        }

        console.log('UserSeeder is done!');
    }
}
