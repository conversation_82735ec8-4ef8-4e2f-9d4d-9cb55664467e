import { Injectable, NotFoundException, HttpException } from '@nestjs/common';
import { RoleRepository } from '~/database/typeorm/repositories/role.repository';
import { DatabaseService } from '~/database/typeorm/database.service';
import { UtilService } from '~/shared/services';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';

@Injectable()
export class RoleService {
    constructor(
        private readonly roleRepository: RoleRepository,
        private readonly utilService: UtilService,
        private readonly database: DatabaseService,
    ) {}

    async create(createRoleDto: CreateRoleDto) {
        const entity = this.roleRepository.create({
            name: createRoleDto.name,
            description: createRoleDto.description,
        });
        const result = await this.roleRepository.save(entity);

        // add permissions to role
        if (createRoleDto.permissionIds?.length) {
            this.roleRepository.createQueryBuilder().relation('permissions').of(result).add(createRoleDto.permissionIds);
        }

        return result;
    }

    async findAll(query: { page: number; perPage: number; sortBy: string; search: string }) {
        const { take, skip, pagination } = this.utilService.getPagination(query);
        const builder = this.roleRepository.createQueryBuilder('entity');

        if (Number(query.perPage) !== 0) builder.take(take).skip(skip);
        if (query.sortBy) builder.orderBy(this.utilService.getSortCondition('entity', query.sortBy));
        if (query.search) builder.andWhere('entity.name ILIKE :name', { name: `%${query.search}%` });

        const [result, total] = await builder.getManyAndCount();
        const totalPages = Math.ceil(total / take);

        return {
            data: result,
            pagination: {
                ...pagination,
                totalRecords: total,
                totalPages: totalPages,
            },
        };
    }

    async findOne(id: number) {
        const result = await this.roleRepository.findOne({ relations: ['permissions'], where: { id } });
        if (!result) throw new NotFoundException('Không tìm thấy quyền này!');

        const { permissions, ...rest } = result;

        return {
            ...rest,
            permissionIds: permissions.length > 0 ? permissions.map((permission) => permission.id) : [],
        };
    }

    async update(id: number, updateRoleDto: UpdateRoleDto) {
        const { permissionIds, ...rest } = updateRoleDto;
        const result = await this.roleRepository.update(id, rest);

        // delete all permissions of role
        await this.roleRepository.removePermissions(id);

        // add permissions to role
        if (permissionIds?.length) {
            await this.roleRepository.createQueryBuilder().relation('permissions').of(id).add(permissionIds);
        }

        return result;
    }

    async remove(id: number) {
        // Check if role is being used by users (only multiple roles)
        const usersWithMultipleRoles = await this.database.usersRole.count({ where: { roleId: id } });

        if (usersWithMultipleRoles > 0) {
            throw new HttpException(`Không thể xóa role này vì có ${usersWithMultipleRoles} người dùng đang sử dụng`, 400);
        }

        await this.roleRepository.removePermissions(id);
        return this.roleRepository.delete(id);
    }

    // Get all users assigned to this role (both single and multiple)
    async getUsersByRole(roleId: number) {
        const role = await this.roleRepository.findOneBy({ id: roleId });
        if (!role) {
            throw new HttpException('Không tìm thấy role', 404);
        }

        // Get users with multiple roles
        const userRoles = await this.database.usersRole.find({
            where: { roleId },
            relations: ['user'],
        });

        return {
            role,
            users: userRoles.map((ur) => ur.user),
            totalUsers: userRoles.length,
        };
    }

    // Get role usage statistics
    async getRoleUsageStats(roleId: number) {
        const role = await this.roleRepository.findOneBy({ id: roleId });
        if (!role) {
            throw new HttpException('Không tìm thấy role', 404);
        }

        const multipleRoleCount = await this.database.usersRole.count({ where: { roleId } });

        return {
            roleId,
            roleName: role.name,
            usersWithMultipleRoles: multipleRoleCount,
            totalUsers: multipleRoleCount,
        };
    }

    // Check if role can be safely deleted
    async canDeleteRole(roleId: number): Promise<boolean> {
        const multipleRoleCount = await this.database.usersRole.count({ where: { roleId } });

        return multipleRoleCount === 0;
    }
}
