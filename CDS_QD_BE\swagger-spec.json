{"openapi": "3.0.0", "paths": {"/test": {"get": {"operationId": "AppController_test", "parameters": [{"name": "string", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}}}, "/auth/login": {"post": {"operationId": "AuthController_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["<PERSON><PERSON>"]}}, "/auth/logout": {"post": {"operationId": "AuthController_logout", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}}, "/auth/forgot-password": {"post": {"operationId": "AuthController_forgotPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["<PERSON><PERSON>"]}}, "/auth/reset-password": {"post": {"operationId": "AuthController_resetPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["<PERSON><PERSON>"]}}, "/auth/renew-token": {"post": {"operationId": "AuthController_renewToken", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RenewTokenDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["<PERSON><PERSON>"]}}, "/profile": {"get": {"operationId": "ProfileController_findOne", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Profile"], "security": [{"bearer": []}]}, "patch": {"operationId": "ProfileController_update", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Profile"], "security": [{"bearer": []}]}}, "/profile/change-password": {"patch": {"operationId": "ProfileController_changePassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Profile"], "security": [{"bearer": []}]}}, "/role": {"post": {"operationId": "RoleController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRoleDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Role"], "security": [{"bearer": []}]}, "get": {"operationId": "RoleController_findAll", "parameters": [{"required": false, "name": "page", "in": "query", "schema": {"default": 1, "type": "number"}}, {"required": false, "name": "perPage", "in": "query", "schema": {"default": 10, "type": "number"}}, {"required": false, "name": "sortBy", "in": "query", "schema": {"default": "id.ASC", "type": "string"}}, {"required": false, "name": "search", "in": "query", "schema": {"type": "string"}}, {"required": false, "name": "type", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Role"], "security": [{"bearer": []}]}}, "/role/{id}": {"get": {"operationId": "RoleController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Role"], "security": [{"bearer": []}]}, "patch": {"operationId": "RoleController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Role"], "security": [{"bearer": []}]}, "delete": {"operationId": "RoleController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Role"], "security": [{"bearer": []}]}}, "/role/{id}/users": {"get": {"operationId": "RoleController_getUsersByRole", "summary": "Get all users assigned to this role", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Role"], "security": [{"bearer": []}]}}, "/role/{id}/stats": {"get": {"operationId": "RoleController_getRoleUsageStats", "summary": "Get role usage statistics", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Role"], "security": [{"bearer": []}]}}, "/role/{id}/can-delete": {"get": {"operationId": "RoleController_canDeleteRole", "summary": "Check if role can be safely deleted", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Role"], "security": [{"bearer": []}]}}, "/permission": {"post": {"operationId": "PermissionController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePermissionDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Permission"], "security": [{"bearer": []}]}, "get": {"operationId": "PermissionController_findAll", "parameters": [{"required": false, "name": "page", "in": "query", "schema": {"default": 1, "type": "number"}}, {"required": false, "name": "perPage", "in": "query", "schema": {"default": 10, "type": "number"}}, {"required": false, "name": "sortBy", "in": "query", "schema": {"default": "id.ASC", "type": "string"}}, {"required": false, "name": "search", "in": "query", "schema": {"type": "string"}}, {"required": false, "name": "type", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Permission"], "security": [{"bearer": []}]}}, "/permission/{id}": {"get": {"operationId": "PermissionController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Permission"], "security": [{"bearer": []}]}, "patch": {"operationId": "PermissionController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Permission"], "security": [{"bearer": []}]}, "delete": {"operationId": "PermissionController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Permission"], "security": [{"bearer": []}]}}, "/media/upload": {"post": {"operationId": "MediaController_upload", "parameters": [], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"201": {"description": ""}}, "tags": ["Media"], "security": [{"bearer": []}]}}, "/media": {"get": {"operationId": "MediaController_findAll", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Media"], "security": [{"bearer": []}]}}, "/media/{id}": {"get": {"operationId": "MediaController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Media"], "security": [{"bearer": []}]}, "delete": {"operationId": "MediaController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Media"], "security": [{"bearer": []}]}}, "/user": {"post": {"operationId": "UserController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["User"], "security": [{"bearer": []}]}, "get": {"operationId": "UserController_findAll", "parameters": [{"required": false, "name": "page", "in": "query", "schema": {"default": 1, "type": "number"}}, {"required": false, "name": "perPage", "in": "query", "schema": {"default": 10, "type": "number"}}, {"required": false, "name": "sortBy", "in": "query", "schema": {"default": "id.ASC", "type": "string"}}, {"required": false, "name": "search", "in": "query", "schema": {"type": "string"}}, {"required": false, "name": "type", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["User"], "security": [{"bearer": []}]}}, "/user/{id}": {"get": {"operationId": "UserController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["User"], "security": [{"bearer": []}]}, "patch": {"operationId": "UserController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["User"], "security": [{"bearer": []}]}, "delete": {"operationId": "UserController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["User"], "security": [{"bearer": []}]}}, "/user/{id}/roles": {"get": {"operationId": "UserController_getUserRoles", "summary": "Get all roles for a user", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["User"], "security": [{"bearer": []}]}, "post": {"operationId": "UserController_assignRolesToUser", "summary": "Assign multiple roles to a user", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignRolesDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["User"], "security": [{"bearer": []}]}}, "/user/{id}/roles/{roleId}": {"delete": {"operationId": "UserController_removeRoleFromUser", "summary": "Remove a specific role from user", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "roleId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["User"], "security": [{"bearer": []}]}}, "/department": {"post": {"operationId": "DepartmentController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Department"], "security": [{"bearer": []}]}, "get": {"operationId": "DepartmentController_findAll", "parameters": [{"required": false, "name": "page", "in": "query", "schema": {"default": 1, "type": "number"}}, {"required": false, "name": "perPage", "in": "query", "schema": {"default": 10, "type": "number"}}, {"required": false, "name": "sortBy", "in": "query", "schema": {"default": "id.ASC", "type": "string"}}, {"required": false, "name": "search", "in": "query", "schema": {"type": "string"}}, {"required": false, "name": "type", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Department"], "security": [{"bearer": []}]}}, "/department/{id}": {"get": {"operationId": "DepartmentController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Department"], "security": [{"bearer": []}]}, "patch": {"operationId": "DepartmentController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Department"], "security": [{"bearer": []}]}, "delete": {"operationId": "DepartmentController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Department"], "security": [{"bearer": []}]}}, "/warehouse/type": {"post": {"operationId": "WarehouseController_createType", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWarehouseTypeDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Warehouse"], "security": [{"bearer": []}]}, "get": {"operationId": "WarehouseController_findAllType", "parameters": [{"required": false, "name": "page", "in": "query", "schema": {"default": 1, "type": "number"}}, {"required": false, "name": "perPage", "in": "query", "schema": {"default": 10, "type": "number"}}, {"required": false, "name": "sortBy", "in": "query", "schema": {"default": "id.ASC", "type": "string"}}, {"required": false, "name": "search", "in": "query", "schema": {"type": "string"}}, {"required": false, "name": "type", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Warehouse"], "security": [{"bearer": []}]}}, "/warehouse": {"post": {"operationId": "WarehouseController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWarehouseDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Warehouse"], "security": [{"bearer": []}]}, "get": {"operationId": "WarehouseController_findAll", "parameters": [{"required": false, "name": "page", "in": "query", "schema": {"default": 1, "type": "number"}}, {"required": false, "name": "perPage", "in": "query", "schema": {"default": 10, "type": "number"}}, {"required": false, "name": "sortBy", "in": "query", "schema": {"default": "id.ASC", "type": "string"}}, {"required": false, "name": "search", "in": "query", "schema": {"type": "string"}}, {"required": false, "name": "type", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Warehouse"], "security": [{"bearer": []}]}}, "/warehouse/type/{id}": {"get": {"operationId": "WarehouseController_findOneType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Warehouse"], "security": [{"bearer": []}]}, "patch": {"operationId": "WarehouseController_updateType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWarehouseTypeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Warehouse"], "security": [{"bearer": []}]}, "delete": {"operationId": "WarehouseController_removeType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Warehouse"], "security": [{"bearer": []}]}}, "/warehouse/{id}": {"get": {"operationId": "WarehouseController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Warehouse"], "security": [{"bearer": []}]}, "patch": {"operationId": "WarehouseController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWarehouseDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Warehouse"], "security": [{"bearer": []}]}, "delete": {"operationId": "WarehouseController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Warehouse"], "security": [{"bearer": []}]}}, "/provider": {"post": {"operationId": "ProviderController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProviderDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Provider"], "security": [{"bearer": []}]}, "get": {"operationId": "ProviderController_findAll", "parameters": [{"required": false, "name": "page", "in": "query", "schema": {"default": 1, "type": "number"}}, {"required": false, "name": "perPage", "in": "query", "schema": {"default": 10, "type": "number"}}, {"required": false, "name": "sortBy", "in": "query", "schema": {"default": "id.ASC", "type": "string"}}, {"required": false, "name": "search", "in": "query", "schema": {"type": "string"}}, {"required": false, "name": "type", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Provider"], "security": [{"bearer": []}]}}, "/provider/{id}": {"get": {"operationId": "ProviderController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Provider"], "security": [{"bearer": []}]}, "patch": {"operationId": "ProviderController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProviderDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Provider"], "security": [{"bearer": []}]}, "delete": {"operationId": "ProviderController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Provider"], "security": [{"bearer": []}]}}}, "info": {"title": " Swagger", "description": "The  API documents", "version": "1.0", "contact": {}, "license": {"name": "CDS-QD.dev", "url": "https://cdsqd.dev"}}, "tags": [], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"LoginDto": {"type": "object", "properties": {"username": {"type": "string", "example": "admin"}, "password": {"type": "string", "example": "admin"}}, "required": ["username", "password"]}, "ForgotPasswordDto": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}}, "required": ["email"]}, "ResetPasswordDto": {"type": "object", "properties": {"token": {"type": "string", "example": "abc"}, "password": {"type": "string", "example": "xyz"}}, "required": ["token", "password"]}, "RenewTokenDto": {"type": "object", "properties": {"refreshToken": {"type": "string", "example": "abc.xyz"}}, "required": ["refreshToken"]}, "UpdateProfileDto": {"type": "object", "properties": {"hoTen": {"type": "string"}, "email": {"type": "string"}, "avatarId": {"type": "string"}, "soDienThoai": {"type": "string"}}, "required": ["hoTen", "email", "avatarId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "ChangePasswordDto": {"type": "object", "properties": {"old_password": {"type": "string"}, "new_password": {"type": "string"}, "confirm_password": {"type": "string"}}, "required": ["old_password", "new_password", "confirm_password"]}, "CreateRoleDto": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "permissionIds": {"type": "array", "items": {"type": "number"}}}, "required": ["name", "description", "permissionIds"]}, "UpdateRoleDto": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "permissionIds": {"type": "array", "items": {"type": "number"}}}}, "CreatePermissionDto": {"type": "object", "properties": {}}, "UpdatePermissionDto": {"type": "object", "properties": {}}, "CreateUserDto": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}, "email": {"type": "string"}, "roleId": {"type": "number"}, "roleIds": {"description": "Array of role IDs for multiple roles (optional)", "example": [1, 2, 3], "type": "array", "items": {"type": "number"}}, "departmentId": {"type": "number"}, "fullName": {"type": "string"}, "areaCode": {"type": "string"}, "phone": {"type": "string"}, "address": {"type": "string"}, "birthday": {"type": "string"}, "gender": {"type": "string"}}, "required": ["username", "password", "email", "roleId", "departmentId", "fullName", "areaCode", "phone", "address", "birthday", "gender"]}, "UpdateUserDto": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}, "email": {"type": "string"}, "roleId": {"type": "number"}, "roleIds": {"description": "Array of role IDs for multiple roles (optional)", "example": [1, 2, 3], "type": "array", "items": {"type": "number"}}, "departmentId": {"type": "number"}, "fullName": {"type": "string"}, "areaCode": {"type": "string"}, "phone": {"type": "string"}, "address": {"type": "string"}, "birthday": {"type": "string"}, "gender": {"type": "string"}}}, "AssignRolesDto": {"type": "object", "properties": {"roleIds": {"description": "Array of role IDs to assign to user", "example": [1, 2, 3], "type": "array", "items": {"type": "number"}}}, "required": ["roleIds"]}, "CreateDepartmentDto": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "description"]}, "UpdateDepartmentDto": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}}}, "CreateWarehouseTypeDto": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "description"]}, "CreateWarehouseDto": {"type": "object", "properties": {"name": {"type": "string"}, "code": {"type": "string"}, "typeId": {"type": "number"}, "description": {"type": "string"}, "address": {"type": "string"}}, "required": ["name", "code", "typeId", "description", "address"]}, "UpdateWarehouseTypeDto": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}}}, "UpdateWarehouseDto": {"type": "object", "properties": {"name": {"type": "string"}, "code": {"type": "string"}, "typeId": {"type": "number"}, "description": {"type": "string"}, "address": {"type": "string"}}}, "CreateProviderDto": {"type": "object", "properties": {"name": {"type": "string"}, "address": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "address", "phone", "email", "description"]}, "UpdateProviderDto": {"type": "object", "properties": {"name": {"type": "string"}, "address": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "description": {"type": "string"}}}}}}