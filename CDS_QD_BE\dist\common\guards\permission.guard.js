"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"PermissionGuard",{enumerable:true,get:function(){return PermissionGuard}});const _common=require("@nestjs/common");const _core=require("@nestjs/core");const _typeorm=require("typeorm");const _constant=require("../constants/constant");const _permissiondecorator=require("../decorators/permission.decorator");const _enum=require("../enums/enum");const _userentity=require("../../database/typeorm/entities/user.entity");const _cacheservice=require("../../shared/services/cache.service");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}const URLs=["auth","docs"];let PermissionGuard=class PermissionGuard{canActivate(context){const req=context.switchToHttp().getRequest();if(URLs.some(url=>req.originalUrl.includes(url)))return true;const permission=this.reflector.getAllAndOverride(_permissiondecorator.PERMISSION_KEY,[context.getHandler(),context.getClass()]);if(!permission)return false;return this.verifyPermission({req:req,permission:permission[0],params:req.params})}async verifyPermission(data){try{if(data.permission===_constant.BYPASS_PERMISSION)return true;const userId=data.req.headers["_userId"];const user=await this.getUser(+userId||0);if(!user)return false;if(this.hasAdminRole(user)){const primaryRoleId=user.userRoles?.[0]?.role?.id;data.req.headers["_roleId"]=primaryRoleId?.toString()||"";data.req.headers["_fullName"]=user.hoTen;return true}const roleIds=this.getAllUserRoleIds(user);if(roleIds.length===0)return false;const permissions=await this.getPermissionsFromMultipleRoles(roleIds);if(permissions.length===0)return false;if(!permissions.some(p=>p.action===data.permission))return false;const primaryRoleId=roleIds[0];data.req.headers["_roleId"]=primaryRoleId?.toString()||"";data.req.headers["_fullName"]=user.hoTen;data.req.headers["_allRoleIds"]=roleIds.join(",");return true}catch(error){console.log("LOG:: error:",error.stack);console.log("LOG:: PermissionGuard:",error.message);return false}}async getPermissions(roleId){const key=`permissions:${roleId}`;const cached=await this.cacheService.getJson(key);if(cached)return cached;const entityManager=this.dataSource.manager;const permissions=await entityManager.query(`
            SELECT p.action
            FROM roles_permissions as rp, permissions as p
            WHERE rp.role_id = ${roleId}
                AND rp.permission_id = p.id
        `);this.cacheService.setJson(key,permissions,_enum.CACHE_TIME.ONE_MONTH);return permissions}async getPermissionsFromMultipleRoles(roleIds){if(!roleIds||roleIds.length===0)return[];const key=`permissions:multiple:${roleIds.sort().join(",")}`;const cached=await this.cacheService.getJson(key);if(cached)return cached;const entityManager=this.dataSource.manager;const permissions=await entityManager.query(`
            SELECT DISTINCT p.action
            FROM roles_permissions as rp, permissions as p
            WHERE rp.role_id IN (${roleIds.join(",")})
                AND rp.permission_id = p.id
        `);this.cacheService.setJson(key,permissions,_enum.CACHE_TIME.ONE_MONTH);return permissions}hasAdminRole(user){if(user.userRoles&&user.userRoles.length>0){return user.userRoles.some(userRole=>userRole.role?.id===_enum.USER_ROLE.ADMIN)}return false}getAllUserRoleIds(user){const roleIds=[];if(user.userRoles&&user.userRoles.length>0){user.userRoles.forEach(userRole=>{if(userRole.role?.id){roleIds.push(userRole.role.id)}})}return roleIds}async getUser(id){const key=`userData:${id}`;const cached=await this.cacheService.getJson(key);if(cached)return cached;const entityManager=this.dataSource.manager;const user=await entityManager.findOne(_userentity.UserEntity,{where:{id:id},select:["id","hoTen"],relations:["userRoles","userRoles.role"]});if(user){this.cacheService.setJson(key,user,_enum.CACHE_TIME.ONE_WEEK)}return user}constructor(reflector,dataSource,cacheService){this.reflector=reflector;this.dataSource=dataSource;this.cacheService=cacheService}};PermissionGuard=_ts_decorate([(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _core.Reflector==="undefined"?Object:_core.Reflector,typeof _typeorm.DataSource==="undefined"?Object:_typeorm.DataSource,typeof _cacheservice.CacheService==="undefined"?Object:_cacheservice.CacheService])],PermissionGuard);
//# sourceMappingURL=permission.guard.js.map