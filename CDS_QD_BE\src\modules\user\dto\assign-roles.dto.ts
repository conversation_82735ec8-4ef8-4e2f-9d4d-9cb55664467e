import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber } from 'class-validator';

export class AssignRolesDto {
    @ApiProperty({ 
        description: 'Array of role IDs to assign to user',
        example: [1, 2, 3],
        type: [Number]
    })
    @IsArray({ message: 'Role IDs phải là một mảng' })
    @IsNotEmpty({ message: 'Role IDs không được để trống' })
    @IsNumber({}, { each: true, message: 'Mỗi role ID phải là một số' })
    roleIds: number[];
}

export class RemoveRoleDto {
    @ApiProperty({ 
        description: 'Role ID to remove from user',
        example: 1
    })
    @IsNotEmpty({ message: 'Role ID không được để trống' })
    @IsNumber({}, { message: 'Role ID phải là một số' })
    roleId: number;
}
