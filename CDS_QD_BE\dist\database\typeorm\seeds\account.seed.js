"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"default",{enumerable:true,get:function(){return AccountSeeder}});const _userentity=require("../entities/user.entity");const _accountentity=require("../entities/account.entity");let AccountSeeder=class AccountSeeder{async run(dataSource,factoryManager){console.log("AccountSeeder is running...");const repository=dataSource.getRepository(_accountentity.AccountEntity);if(!await repository.countBy({username:"admin"})){await repository.insert([{username:"admin",password:"$2b$08$NurMoRDe0qIYY1SL8EQFT.WUTbCf8u2gk7imco2XFlapibRscC2v.",salt:"$2b$08$NurMoRDe0qIYY1SL8EQFT.",isActive:true}])}if(!await repository.countBy({username:"user"})){await repository.insert([{username:"user",password:"$2b$08$W7QK7P7R43qpdRP3DQVuMuQGygEYWhMH1ehjMljFjJdMtyCzqPo8u",salt:"$2b$08$W7QK7P7R43qpdRP3DQVuMu",isActive:true}])}const accountFactory=factoryManager.get(_accountentity.AccountEntity);const userFactory=factoryManager.get(_userentity.UserEntity);const account=await accountFactory.save();await userFactory.save({accountId:account.id});console.log("AccountSeeder is done!")}};
//# sourceMappingURL=account.seed.js.map