import { Injectable, HttpException } from '@nestjs/common';
import { DatabaseService } from '~/database/typeorm/database.service';

@Injectable()
export class UsersRoleService {
    constructor(private readonly database: DatabaseService) {}

    // Assign multiple roles to a user
    async assignRolesToUser(userId: number, roleIds: number[]) {
        // Validate user exists
        const user = await this.database.user.findOneBy({ id: userId });
        if (!user) {
            throw new HttpException('Không tìm thấy người dùng', 404);
        }

        // Validate roles exist
        const roles = await this.database.role.findByIds(roleIds);
        if (roles.length !== roleIds.length) {
            throw new HttpException('Một hoặc nhiều role không tồn tại', 400);
        }

        // Remove existing user roles to avoid duplicates
        await this.database.usersRole.delete({ userId });

        // Create new user-role relationships
        const userRoles = roleIds.map(roleId => ({
            userId,
            roleId
        }));

        return this.database.usersRole.save(userRoles);
    }

    // Add a single role to user (without removing existing ones)
    async addRoleToUser(userId: number, roleId: number) {
        // Check if user exists
        const user = await this.database.user.findOneBy({ id: userId });
        if (!user) {
            throw new HttpException('Không tìm thấy người dùng', 404);
        }

        // Check if role exists
        const role = await this.database.role.findOneBy({ id: roleId });
        if (!role) {
            throw new HttpException('Không tìm thấy role', 404);
        }

        // Check if user already has this role
        const existingUserRole = await this.database.usersRole.findOne({
            where: { userId, roleId }
        });

        if (existingUserRole) {
            throw new HttpException('Người dùng đã có role này', 400);
        }

        // Create new user-role relationship
        return this.database.usersRole.save({ userId, roleId });
    }

    // Remove a specific role from user
    async removeRoleFromUser(userId: number, roleId: number) {
        const result = await this.database.usersRole.delete({ userId, roleId });
        
        if (result.affected === 0) {
            throw new HttpException('Không tìm thấy quan hệ user-role để xóa', 404);
        }

        return result;
    }

    // Get all roles for a user
    async getUserRoles(userId: number) {
        return this.database.usersRole.find({
            where: { userId },
            relations: ['role']
        });
    }

    // Get all users for a role
    async getRoleUsers(roleId: number) {
        return this.database.usersRole.find({
            where: { roleId },
            relations: ['user']
        });
    }

    // Check if user has a specific role
    async userHasRole(userId: number, roleId: number): Promise<boolean> {
        const userRole = await this.database.usersRole.findOne({
            where: { userId, roleId }
        });
        return !!userRole;
    }

    // Bulk assign roles to multiple users
    async bulkAssignRoles(userIds: number[], roleIds: number[]) {
        const userRoles = [];
        
        for (const userId of userIds) {
            for (const roleId of roleIds) {
                userRoles.push({ userId, roleId });
            }
        }

        // Remove existing relationships for these users
        await this.database.usersRole.delete({ userId: { $in: userIds } as any });

        return this.database.usersRole.save(userRoles);
    }

    // Remove all roles from a user
    async removeAllUserRoles(userId: number) {
        return this.database.usersRole.delete({ userId });
    }

    // Remove all users from a role
    async removeAllRoleUsers(roleId: number) {
        return this.database.usersRole.delete({ roleId });
    }
}
