{"version": 3, "sources": ["../../../src/shared/services/token.service.ts"], "sourcesContent": ["import { HttpException, Injectable } from '@nestjs/common';\nimport { ConfigService } from '@nestjs/config';\nimport * as bcrypt from 'bcrypt';\nimport { createHash } from 'crypto';\nimport * as jwt from 'jsonwebtoken';\nimport { CACHE_TIME } from '~/common/enums/enum';\nimport { UserEntity } from '~/database/typeorm/entities/user.entity';\nimport { AccountRepository } from '~/database/typeorm/repositories/account.repository';\nimport { CacheService } from '~/shared/services/cache.service';\nimport { UtilService } from '~/shared/services/util.service';\n\n@Injectable()\nexport class TokenService {\n    constructor(\n        private configService: ConfigService,\n        private readonly accountRepository: AccountRepository,\n        private readonly utilService: UtilService,\n        private readonly cacheService: CacheService,\n    ) {\n        //\n    }\n\n    /* AUTH TOKEN */\n    public createAuthToken(data: { id: number; password: string; secretToken: string }) {\n        try {\n            const { id, secretToken } = data;\n            // Tạo tokenVersion để có thể vô hiệu hóa token khi cần\n            const tokenVersion = this.utilService.generateString(16);\n            const { authTokenSecret, authTokenName, authExpiresIn } = this.configService.get('token');\n            const exp = Math.floor(Date.now() / 1000) + authExpiresIn; // authExpiresIn: seconds\n            const payload = {\n                exp,\n                id,\n                authTokenName,\n                secretToken,\n                jti: tokenVersion, // JWT ID giúp vô hiệu hóa token\n            };\n\n            const authToken = jwt.sign(payload, authTokenSecret);\n            const authTokenExpiresIn = exp;\n\n            return { authToken, authTokenExpiresIn };\n        } catch (err) {\n            console.log('createAuthToken error', err);\n            throw new HttpException('Unable to create authToken', 500);\n        }\n    }\n\n    // verify the auth token, return usereID\n    public async verifyAuthToken(data: { authToken: string }): Promise<{ id: string; user: UserEntity }> {\n        try {\n            if (!data.authToken) return { id: null, user: null };\n\n            const { authToken } = data;\n            const jwtRegex = /(^[A-Za-z0-9-_]*\\.[A-Za-z0-9-_]*\\.[A-Za-z0-9-_]*$)/;\n            const res = { id: null, user: null };\n            if (RegExp(jwtRegex).exec(authToken)) {\n                const { authTokenName, tokenData } = await this.getTokentData(authToken);\n                const account = await this.getAccount(tokenData.id);\n\n                if (process.env.LOGIN_SINGLE_DEVICE === 'true' && tokenData.secretToken && account?.secretToken !== tokenData.secretToken) return res;\n\n                if (account && authTokenName === tokenData.authTokenName) {\n                    res.id = account.id;\n                    res.user = account.user;\n                }\n            }\n\n            return res;\n        } catch (err) {\n            console.log('verifyAuthToken error', err);\n            return { id: null, user: null };\n        }\n    }\n\n    private async getAccount(id: number) {\n        const key = `account:${id}`;\n        const cached = await this.cacheService.getJson(key);\n        if (cached) return cached;\n\n        const account = await this.accountRepository.findOne({\n            select: ['id', 'password', 'secretToken'],\n            where: { id: id },\n            relations: ['user', 'user.userRoles', 'user.userRoles.role'],\n        });\n\n        this.cacheService.setJson(key, account, CACHE_TIME.ONE_HOUR);\n        this.cacheService.setJson(`userData:${account?.user?.id}`, account?.user, CACHE_TIME.ONE_WEEK);\n        return account;\n    }\n\n    private async getTokentData(authToken: string) {\n        const cacheKey = `tokenData:${authToken}`;\n        const tokenDataCached = await this.cacheService.getJson(cacheKey);\n        if (tokenDataCached) return tokenDataCached;\n\n        const { authTokenSecret, authTokenName } = this.configService.get('token');\n        const tokenData = jwt.verify(authToken, authTokenSecret);\n        const res = {\n            tokenData: {\n                id: tokenData['id'],\n                secretToken: tokenData['secretToken'],\n                authTokenName: tokenData['authTokenName'],\n                jti: tokenData['jti'],\n            },\n            authTokenName,\n        };\n\n        this.cacheService.setJson(cacheKey, res, CACHE_TIME.THIRTY_MINUTES);\n        return res;\n    }\n    /* AUTH TOKEN */\n\n    /* REFRESH TOKEN */\n    public createRefreshToken(data: { id: number; password: string; secretToken: string }) {\n        try {\n            const { id, secretToken } = data;\n            // Không sử dụng password trong payload\n            const tokenVersion = this.utilService.generateString(16);\n            const { refreshTokenSecret, refreshTokenName, refreshExpiresIn } = this.configService.get('token');\n            const exp = Math.floor(Date.now() / 1000) + refreshExpiresIn; // authExpiresIn: seconds\n\n            const payload = {\n                exp,\n                id,\n                refreshTokenName,\n                secretToken,\n                jti: tokenVersion,\n            };\n\n            const refreshToken = jwt.sign(payload, refreshTokenSecret);\n            const refreshTokenExpiresIn = exp;\n\n            return { refreshToken, refreshTokenExpiresIn };\n        } catch (err) {\n            console.log('createRefreshToken error', err);\n            throw new Error('createRefreshToken error');\n        }\n    }\n\n    public verifyRefreshToken(data: { refreshToken: string }) {\n        try {\n            const { refreshToken } = data;\n            const { refreshTokenSecret, refreshTokenName } = this.configService.get('token');\n            const tokenData: any = jwt.verify(refreshToken, refreshTokenSecret);\n\n            console.log('Verify refresh token data', tokenData);\n\n            if (refreshTokenName === tokenData.refreshTokenName) {\n                return {\n                    id: tokenData.id,\n                    secretToken: tokenData.secretToken,\n                    jti: tokenData.jti,\n                };\n            }\n\n            return null;\n        } catch (err) {\n            console.log('verifyRefreshToken error', err);\n            return null;\n        }\n    }\n    /* REFRESH TOKEN */\n\n    public validateUrl(url: string): string {\n        const removedQuery = url.split('?');\n        const arr = removedQuery[0].split('/');\n        for (let i = 0; i < arr.length; i++) {\n            if (arr[i] && (!isNaN(+arr[i]) || arr[i].includes('0_'))) {\n                arr[i] = ':id';\n            }\n        }\n        return arr.join('/');\n    }\n\n    public static sha1(str: string) {\n        const shasum = createHash('sha1');\n        shasum.update(str);\n        return shasum.digest('hex');\n    }\n\n    hashPassword(password: string) {\n        const salt = bcrypt.genSaltSync(Number(process.env.SALT_ROUNDS) || 8);\n        const hash = bcrypt.hashSync(password, salt);\n\n        return { salt, hash };\n    }\n\n    isPasswordCorrect(plainPassword: string, hash: string) {\n        return bcrypt.compareSync(plainPassword, hash);\n    }\n}\n"], "names": ["TokenService", "createAuthToken", "data", "id", "secretToken", "tokenVersion", "utilService", "generateString", "authTokenSecret", "authTokenName", "authExpiresIn", "configService", "get", "exp", "Math", "floor", "Date", "now", "payload", "jti", "authToken", "jwt", "sign", "authTokenExpiresIn", "err", "console", "log", "HttpException", "verifyAuthToken", "user", "jwtRegex", "res", "RegExp", "exec", "tokenData", "getTokentData", "account", "getAccount", "process", "env", "LOGIN_SINGLE_DEVICE", "key", "cached", "cacheService", "get<PERSON>son", "accountRepository", "findOne", "select", "where", "relations", "<PERSON><PERSON><PERSON>", "CACHE_TIME", "ONE_HOUR", "ONE_WEEK", "cache<PERSON>ey", "tokenDataCached", "verify", "THIRTY_MINUTES", "createRefreshToken", "refreshTokenSecret", "refreshTokenName", "refreshExpiresIn", "refreshToken", "refreshTokenExpiresIn", "Error", "verifyRefreshToken", "validateUrl", "url", "<PERSON><PERSON><PERSON><PERSON>", "split", "arr", "i", "length", "isNaN", "includes", "join", "sha1", "str", "shasum", "createHash", "update", "digest", "hashPassword", "password", "salt", "bcrypt", "genSaltSync", "Number", "SALT_ROUNDS", "hash", "hashSync", "isPasswordCorrect", "plainPassword", "compareSync", "constructor"], "mappings": "oGAYaA,sDAAAA,sCAZ6B,wCACZ,+EACN,iCACG,6EACN,qCACM,4DAEO,sFACL,8CACD,miDAGrB,IAAA,AAAMA,aAAN,MAAMA,aAWT,AAAOC,gBAAgBC,IAA2D,CAAE,CAChF,GAAI,CACA,KAAM,CAAEC,EAAE,CAAEC,WAAW,CAAE,CAAGF,KAE5B,MAAMG,aAAe,IAAI,CAACC,WAAW,CAACC,cAAc,CAAC,IACrD,KAAM,CAAEC,eAAe,CAAEC,aAAa,CAAEC,aAAa,CAAE,CAAG,IAAI,CAACC,aAAa,CAACC,GAAG,CAAC,SACjF,MAAMC,IAAMC,KAAKC,KAAK,CAACC,KAAKC,GAAG,GAAK,KAAQP,cAC5C,MAAMQ,QAAU,CACZL,IACAV,GACAM,cACAL,YACAe,IAAKd,YACT,EAEA,MAAMe,UAAYC,cAAIC,IAAI,CAACJ,QAASV,iBACpC,MAAMe,mBAAqBV,IAE3B,MAAO,CAAEO,UAAWG,kBAAmB,CAC3C,CAAE,MAAOC,IAAK,CACVC,QAAQC,GAAG,CAAC,wBAAyBF,IACrC,OAAM,IAAIG,qBAAa,CAAC,6BAA8B,IAC1D,CACJ,CAGA,MAAaC,gBAAgB1B,IAA2B,CAA6C,CACjG,GAAI,CACA,GAAI,CAACA,KAAKkB,SAAS,CAAE,MAAO,CAAEjB,GAAI,KAAM0B,KAAM,IAAK,EAEnD,KAAM,CAAET,SAAS,CAAE,CAAGlB,KACtB,MAAM4B,SAAW,qDACjB,MAAMC,IAAM,CAAE5B,GAAI,KAAM0B,KAAM,IAAK,EACnC,GAAIG,OAAOF,UAAUG,IAAI,CAACb,WAAY,CAClC,KAAM,CAAEX,aAAa,CAAEyB,SAAS,CAAE,CAAG,MAAM,IAAI,CAACC,aAAa,CAACf,WAC9D,MAAMgB,QAAU,MAAM,IAAI,CAACC,UAAU,CAACH,UAAU/B,EAAE,EAElD,GAAImC,QAAQC,GAAG,CAACC,mBAAmB,GAAK,QAAUN,UAAU9B,WAAW,EAAIgC,SAAShC,cAAgB8B,UAAU9B,WAAW,CAAE,OAAO2B,IAElI,GAAIK,SAAW3B,gBAAkByB,UAAUzB,aAAa,CAAE,CACtDsB,IAAI5B,EAAE,CAAGiC,QAAQjC,EAAE,AACnB4B,CAAAA,IAAIF,IAAI,CAAGO,QAAQP,IAAI,AAC3B,CACJ,CAEA,OAAOE,GACX,CAAE,MAAOP,IAAK,CACVC,QAAQC,GAAG,CAAC,wBAAyBF,KACrC,MAAO,CAAErB,GAAI,KAAM0B,KAAM,IAAK,CAClC,CACJ,CAEA,MAAcQ,WAAWlC,EAAU,CAAE,CACjC,MAAMsC,IAAM,CAAC,QAAQ,EAAEtC,GAAG,CAAC,CAC3B,MAAMuC,OAAS,MAAM,IAAI,CAACC,YAAY,CAACC,OAAO,CAACH,KAC/C,GAAIC,OAAQ,OAAOA,OAEnB,MAAMN,QAAU,MAAM,IAAI,CAACS,iBAAiB,CAACC,OAAO,CAAC,CACjDC,OAAQ,CAAC,KAAM,WAAY,cAAc,CACzCC,MAAO,CAAE7C,GAAIA,EAAG,EAChB8C,UAAW,CAAC,OAAQ,iBAAkB,sBAAsB,AAChE,GAEA,IAAI,CAACN,YAAY,CAACO,OAAO,CAACT,IAAKL,QAASe,gBAAU,CAACC,QAAQ,EAC3D,IAAI,CAACT,YAAY,CAACO,OAAO,CAAC,CAAC,SAAS,EAAEd,SAASP,MAAM1B,GAAG,CAAC,CAAEiC,SAASP,KAAMsB,gBAAU,CAACE,QAAQ,EAC7F,OAAOjB,OACX,CAEA,MAAcD,cAAcf,SAAiB,CAAE,CAC3C,MAAMkC,SAAW,CAAC,UAAU,EAAElC,UAAU,CAAC,CACzC,MAAMmC,gBAAkB,MAAM,IAAI,CAACZ,YAAY,CAACC,OAAO,CAACU,UACxD,GAAIC,gBAAiB,OAAOA,gBAE5B,KAAM,CAAE/C,eAAe,CAAEC,aAAa,CAAE,CAAG,IAAI,CAACE,aAAa,CAACC,GAAG,CAAC,SAClE,MAAMsB,UAAYb,cAAImC,MAAM,CAACpC,UAAWZ,iBACxC,MAAMuB,IAAM,CACRG,UAAW,CACP/B,GAAI+B,SAAS,CAAC,KAAK,CACnB9B,YAAa8B,SAAS,CAAC,cAAc,CACrCzB,cAAeyB,SAAS,CAAC,gBAAgB,CACzCf,IAAKe,SAAS,CAAC,MAAM,AACzB,EACAzB,aACJ,EAEA,IAAI,CAACkC,YAAY,CAACO,OAAO,CAACI,SAAUvB,IAAKoB,gBAAU,CAACM,cAAc,EAClE,OAAO1B,GACX,CAIA,AAAO2B,mBAAmBxD,IAA2D,CAAE,CACnF,GAAI,CACA,KAAM,CAAEC,EAAE,CAAEC,WAAW,CAAE,CAAGF,KAE5B,MAAMG,aAAe,IAAI,CAACC,WAAW,CAACC,cAAc,CAAC,IACrD,KAAM,CAAEoD,kBAAkB,CAAEC,gBAAgB,CAAEC,gBAAgB,CAAE,CAAG,IAAI,CAAClD,aAAa,CAACC,GAAG,CAAC,SAC1F,MAAMC,IAAMC,KAAKC,KAAK,CAACC,KAAKC,GAAG,GAAK,KAAQ4C,iBAE5C,MAAM3C,QAAU,CACZL,IACAV,GACAyD,iBACAxD,YACAe,IAAKd,YACT,EAEA,MAAMyD,aAAezC,cAAIC,IAAI,CAACJ,QAASyC,oBACvC,MAAMI,sBAAwBlD,IAE9B,MAAO,CAAEiD,aAAcC,qBAAsB,CACjD,CAAE,MAAOvC,IAAK,CACVC,QAAQC,GAAG,CAAC,2BAA4BF,IACxC,OAAM,IAAIwC,MAAM,2BACpB,CACJ,CAEA,AAAOC,mBAAmB/D,IAA8B,CAAE,CACtD,GAAI,CACA,KAAM,CAAE4D,YAAY,CAAE,CAAG5D,KACzB,KAAM,CAAEyD,kBAAkB,CAAEC,gBAAgB,CAAE,CAAG,IAAI,CAACjD,aAAa,CAACC,GAAG,CAAC,SACxE,MAAMsB,UAAiBb,cAAImC,MAAM,CAACM,aAAcH,oBAEhDlC,QAAQC,GAAG,CAAC,4BAA6BQ,WAEzC,GAAI0B,mBAAqB1B,UAAU0B,gBAAgB,CAAE,CACjD,MAAO,CACHzD,GAAI+B,UAAU/B,EAAE,CAChBC,YAAa8B,UAAU9B,WAAW,CAClCe,IAAKe,UAAUf,GAAG,AACtB,CACJ,CAEA,OAAO,IACX,CAAE,MAAOK,IAAK,CACVC,QAAQC,GAAG,CAAC,2BAA4BF,KACxC,OAAO,IACX,CACJ,CAGA,AAAO0C,YAAYC,GAAW,CAAU,CACpC,MAAMC,aAAeD,IAAIE,KAAK,CAAC,KAC/B,MAAMC,IAAMF,YAAY,CAAC,EAAE,CAACC,KAAK,CAAC,KAClC,IAAK,IAAIE,EAAI,EAAGA,EAAID,IAAIE,MAAM,CAAED,IAAK,CACjC,GAAID,GAAG,CAACC,EAAE,EAAK,CAAA,CAACE,MAAM,CAACH,GAAG,CAACC,EAAE,GAAKD,GAAG,CAACC,EAAE,CAACG,QAAQ,CAAC,KAAI,EAAI,CACtDJ,GAAG,CAACC,EAAE,CAAG,KACb,CACJ,CACA,OAAOD,IAAIK,IAAI,CAAC,IACpB,CAEA,OAAcC,KAAKC,GAAW,CAAE,CAC5B,MAAMC,OAASC,GAAAA,kBAAU,EAAC,QAC1BD,OAAOE,MAAM,CAACH,KACd,OAAOC,OAAOG,MAAM,CAAC,MACzB,CAEAC,aAAaC,QAAgB,CAAE,CAC3B,MAAMC,KAAOC,QAAOC,WAAW,CAACC,OAAOjD,QAAQC,GAAG,CAACiD,WAAW,GAAK,GACnE,MAAMC,KAAOJ,QAAOK,QAAQ,CAACP,SAAUC,MAEvC,MAAO,CAAEA,KAAMK,IAAK,CACxB,CAEAE,kBAAkBC,aAAqB,CAAEH,IAAY,CAAE,CACnD,OAAOJ,QAAOQ,WAAW,CAACD,cAAeH,KAC7C,CAjLAK,YACI,AAAQnF,aAA4B,CACpC,AAAiBkC,iBAAoC,CACrD,AAAiBvC,WAAwB,CACzC,AAAiBqC,YAA0B,CAC7C,MAJUhC,cAAAA,mBACSkC,kBAAAA,uBACAvC,YAAAA,iBACAqC,aAAAA,YAGrB,CA2KJ"}