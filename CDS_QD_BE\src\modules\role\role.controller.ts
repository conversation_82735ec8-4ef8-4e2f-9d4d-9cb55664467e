import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiQ<PERSON>y, ApiT<PERSON>s, ApiOperation } from '@nestjs/swagger';
import { Permission } from '~/common/decorators/permission.decorator';
import { FilterDto } from '~/common/dtos/filter.dto';
import { UtilService } from '~/shared/services';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { RoleService } from './role.service';

@ApiTags('Role')
@ApiBearerAuth()
@Controller('role')
export class RoleController {
    constructor(private readonly roleService: RoleService, private readonly utilService: UtilService) {}

    @Permission('role:create')
    @Post()
    create(@Body() createRoleDto: CreateRoleDto) {
        return this.roleService.create(createRoleDto);
    }

    @Permission('role:findAll')
    @Get()
    @ApiQuery({ type: FilterDto })
    findAll(@Query() queries) {
        return this.roleService.findAll({ ...queries });
    }

    @Permission('role:findOne')
    @Get(':id')
    findOne(@Param('id') id: string) {
        return this.roleService.findOne(+id);
    }

    @Permission('role:update')
    @Patch(':id')
    update(@Param('id') id: string, @Body() updateRoleDto: UpdateRoleDto) {
        return this.roleService.update(+id, updateRoleDto);
    }

    @Permission('role:remove')
    @Delete(':id')
    remove(@Param('id') id: string) {
        return this.roleService.remove(+id);
    }

    // Multiple users management endpoints
    @Permission('role:findUsers')
    @Get(':id/users')
    @ApiOperation({ summary: 'Get all users assigned to this role' })
    getUsersByRole(@Param('id') id: string) {
        return this.roleService.getUsersByRole(+id);
    }

    @Permission('role:stats')
    @Get(':id/stats')
    @ApiOperation({ summary: 'Get role usage statistics' })
    getRoleUsageStats(@Param('id') id: string) {
        return this.roleService.getRoleUsageStats(+id);
    }

    @Permission('role:canDelete')
    @Get(':id/can-delete')
    @ApiOperation({ summary: 'Check if role can be safely deleted' })
    canDeleteRole(@Param('id') id: string) {
        return this.roleService.canDeleteRole(+id);
    }
}
