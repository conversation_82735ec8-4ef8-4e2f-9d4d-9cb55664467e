{"version": 3, "sources": ["../../../src/modules/user/user.controller.ts"], "sourcesContent": ["import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common';\nimport { ApiBearerAuth, ApiQuery, ApiTags, ApiOperation } from '@nestjs/swagger';\nimport { Permission } from '~/common/decorators/permission.decorator';\nimport { FilterDto } from '~/common/dtos/filter.dto';\nimport { CreateUserDto } from './dto/create-user.dto';\nimport { UpdateUserDto } from './dto/update-user.dto';\nimport { AssignRolesDto } from './dto/assign-roles.dto';\nimport { UserService } from './user.service';\n\n@ApiTags('User')\n@ApiBearerAuth()\n@Controller('user')\nexport class UserController {\n    constructor(private readonly userService: UserService) {}\n\n    @Permission('user:create')\n    @Post()\n    create(@Body() createUserDto: CreateUserDto) {\n        return this.userService.create(createUserDto);\n    }\n\n    @Permission('user:findAll')\n    @Get()\n    @ApiQuery({ type: FilterDto })\n    findAll(@Query() queries) {\n        return this.userService.findAll({ ...queries });\n    }\n\n    @Permission('user:findOne')\n    @Get(':id')\n    findOne(@Param('id') id: string) {\n        return this.userService.findOne(+id);\n    }\n\n    @Permission('user:update')\n    @Patch(':id')\n    update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {\n        return this.userService.update(+id, updateUserDto);\n    }\n\n    @Permission('user:remove')\n    @Delete(':id')\n    remove(@Param('id') id: string) {\n        return this.userService.remove(+id);\n    }\n\n    // Multiple roles management endpoints\n    @Permission('user:findRoles')\n    @Get(':id/roles')\n    @ApiOperation({ summary: 'Get all roles for a user' })\n    getUserRoles(@Param('id') id: string) {\n        return this.userService.getAllUserRoles(+id);\n    }\n\n    @Permission('user:assignRoles')\n    @Post(':id/roles')\n    @ApiOperation({ summary: 'Assign multiple roles to a user' })\n    assignRolesToUser(@Param('id') id: string, @Body() assignRolesDto: AssignRolesDto) {\n        return this.userService.assignRolesToUser(+id, assignRolesDto.roleIds);\n    }\n\n    @Permission('user:removeRole')\n    @Delete(':id/roles/:roleId')\n    @ApiOperation({ summary: 'Remove a specific role from user' })\n    removeRoleFromUser(@Param('id') id: string, @Param('roleId') roleId: string) {\n        return this.userService.removeRoleFromUser(+id, +roleId);\n    }\n}\n"], "names": ["UserController", "create", "createUserDto", "userService", "findAll", "queries", "findOne", "id", "update", "updateUserDto", "remove", "getUserRoles", "getAllUserRoles", "assignRolesToUser", "assignRolesDto", "roleIds", "removeRoleFromUser", "roleId", "constructor", "type", "Filter<PERSON><PERSON>", "summary"], "mappings": "oGAYaA,wDAAAA,wCAZ4D,yCACV,sDACpC,yEACD,6DACI,sDACA,uDACC,qDACH,sqBAKrB,IAAA,AAAMA,eAAN,MAAMA,eAGT,AAEAC,OAAO,AAAQC,aAA4B,CAAE,CACzC,OAAO,IAAI,CAACC,WAAW,CAACF,MAAM,CAACC,cACnC,CAEA,AAGAE,QAAQ,AAASC,OAAO,CAAE,CACtB,OAAO,IAAI,CAACF,WAAW,CAACC,OAAO,CAAC,CAAE,GAAGC,OAAO,AAAC,EACjD,CAEA,AAEAC,QAAQ,AAAaC,EAAU,CAAE,CAC7B,OAAO,IAAI,CAACJ,WAAW,CAACG,OAAO,CAAC,CAACC,GACrC,CAEA,AAEAC,OAAO,AAAaD,EAAU,CAAE,AAAQE,aAA4B,CAAE,CAClE,OAAO,IAAI,CAACN,WAAW,CAACK,MAAM,CAAC,CAACD,GAAIE,cACxC,CAEA,AAEAC,OAAO,AAAaH,EAAU,CAAE,CAC5B,OAAO,IAAI,CAACJ,WAAW,CAACO,MAAM,CAAC,CAACH,GACpC,CAGA,AAGAI,aAAa,AAAaJ,EAAU,CAAE,CAClC,OAAO,IAAI,CAACJ,WAAW,CAACS,eAAe,CAAC,CAACL,GAC7C,CAEA,AAGAM,kBAAkB,AAAaN,EAAU,CAAE,AAAQO,cAA8B,CAAE,CAC/E,OAAO,IAAI,CAACX,WAAW,CAACU,iBAAiB,CAAC,CAACN,GAAIO,eAAeC,OAAO,CACzE,CAEA,AAGAC,mBAAmB,AAAaT,EAAU,CAAE,AAAiBU,MAAc,CAAE,CACzE,OAAO,IAAI,CAACd,WAAW,CAACa,kBAAkB,CAAC,CAACT,GAAI,CAACU,OACrD,CArDAC,YAAY,AAAiBf,WAAwB,CAAE,MAA1BA,YAAAA,WAA2B,CAsD5D,kdA5CgBgB,KAAMC,oBAAS,6xCA0BXC,QAAS,yWAOTA,QAAS,wfAOTA,QAAS"}