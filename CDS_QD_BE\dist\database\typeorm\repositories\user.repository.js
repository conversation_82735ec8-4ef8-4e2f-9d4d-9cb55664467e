"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"UserRepository",{enumerable:true,get:function(){return UserRepository}});const _common=require("@nestjs/common");const _typeorm=require("typeorm");const _userentity=require("../entities/user.entity");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let UserRepository=class UserRepository extends _typeorm.Repository{async getAllUserRoles(userId){const user=await this.findOne({where:{id:userId},relations:["userRoles","userRoles.role"]});if(!user)return[];const roles=[];if(user.userRoles&&user.userRoles.length>0){user.userRoles.forEach(userRole=>{if(userRole.role){roles.push(userRole.role)}})}return roles}constructor(dataSource){super(_userentity.UserEntity,dataSource.createEntityManager()),this.dataSource=dataSource,this.findOneUserWithAllRelationsById=id=>{return this.findOne({where:{id:id},relations:["userRoles","userRoles.role","avatar","account"]})},this.findOneUserWithAllRolesById=id=>{return this.findOne({where:{id:id},relations:["userRoles","userRoles.role","avatar","account"]})},this.findOneWithRalations=({where,relations})=>{const builder=this.createQueryBuilder("entity");if(where){builder.where(where)}if(relations.length){relations.forEach(relation=>{builder.leftJoinAndMapOne(`entity.${relation}`,`entity.${relation}`,relation,`${relation}.id = entity.${relation}Id`)})}return builder.getOne()},this.findOneUserWithRolesByAccountId=accountId=>{return this.findOne({where:{accountId},relations:["userRoles","userRoles.role"]})}}};UserRepository=_ts_decorate([(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _typeorm.DataSource==="undefined"?Object:_typeorm.DataSource])],UserRepository);
//# sourceMappingURL=user.repository.js.map