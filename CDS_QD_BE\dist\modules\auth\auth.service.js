"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"AuthService",{enumerable:true,get:function(){return AuthService}});const _common=require("@nestjs/common");const _services=require("../../shared/services");const _enum=require("../../common/enums/enum");const _accountrepository=require("../../database/typeorm/repositories/account.repository");const _userrepository=require("../../database/typeorm/repositories/user.repository");const _mailservice=require("../mail/mail.service");const _cacheservice=require("../../shared/services/cache.service");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let AuthService=class AuthService{async login(data){try{const account=await this.accountRepository.findOne({select:["id","username","password","secretToken","isActive"],where:{username:data.username}});if(!account){throw new _common.UnauthorizedException("Wrong username or password")}if(!account.isActive){throw new _common.UnauthorizedException("User disabled")}if(!this.tokenService.isPasswordCorrect(data.password,account.password)){throw new _common.UnauthorizedException("Wrong username or password")}const secretToken=this.utilService.generateString();const tokenData=this.tokenService.createAuthToken({id:account.id,password:"",secretToken});const refreshTokenData=this.tokenService.createRefreshToken({id:account.id,password:"",secretToken});this.accountRepository.update(account.id,{secretToken});const user=await this.userRepository.findOneUserWithRolesByAccountId(account.id);if(!user||user.status!==_enum.USER_STATUS.ACTIVE)throw new _common.UnauthorizedException("User not found");const roles=await this.userRepository.getAllUserRoles(user.id);this.cacheService.delete(`account:${account.id}`);return{result:true,message:"Login successfully",data:{id:account.id,session:tokenData.authToken,expired:tokenData.authTokenExpiresIn,refreshToken:refreshTokenData.refreshToken,roles:roles}}}catch(err){throw new _common.UnauthorizedException("Login error")}}async logout(data){const user=await this.tokenService.verifyAuthToken({authToken:data.session});if(user.id){const accountId=(await this.userRepository.findOneBy({id:+user.id})).accountId;if(accountId){this.accountRepository.update(accountId,{secretToken:null});this.cacheService.delete(`account:${accountId}`)}}return{result:true,message:"Success",data:null}}async forgotPassword(data){try{if(!this.utilService.validateEmail(data.email)){return{result:false,message:"Email is invalid",data:null}}const user=await this.userRepository.findOne({select:["email","hoTen","status","account"],where:{email:data.email},relations:["account"]});if(!user){return{result:false,message:"User not found",data:null}}if(user.status===_enum.USER_STATUS.DISABLED){return{result:false,message:"User disabled",data:{is_active:false}}}const encrypted=this.utilService.aesEncrypt({email:user.email,password:user.account.password},this.RESETPASSWORDTIMEOUT);const link=`${process.env.FE_URL}/reset-password?token=${encrypted}`;this.mailService.sendForgotPassword({emailTo:user.email,subject:"Reset your password",name:user.hoTen,link:link});return{result:true,message:"Reset-password link has been sent to your email",data:null}}catch(err){throw new _common.InternalServerErrorException({result:false,message:"Forgot password error",data:null,statusCode:500})}}async resetPassword(data){try{const validateToken=this.validateToken(data.token);if(!validateToken.result){return{result:false,message:"Token invalid",data:null}}const email=validateToken.email;const password=validateToken.password;const user=await this.userRepository.findOne({select:["id","account"],where:{email:email},relations:["account"]});if(!user){return{result:false,message:"User not found",data:null}}if(user.account.password!==password){return{result:false,message:"Token expired",data:null}}const{salt,hash}=this.tokenService.hashPassword(data.password);const res=await this.accountRepository.update(user.account.id,{password:hash,salt});return{result:res.affected>0,message:res.affected>0?"Password reset successfully":"Cannot reset password",data:null}}catch(err){throw new _common.InternalServerErrorException({result:false,message:"Reset password error",data:null,statusCode:500})}}async renewAuthToken(data){const refreshTokenData=this.tokenService.verifyRefreshToken({refreshToken:data.refreshToken});if(!refreshTokenData){return{session:null,refreshToken:null}}const account=await this.accountRepository.findOne({select:["id","secretToken"],where:{id:refreshTokenData.id}});if(!account){return{session:null,refreshToken:null}}const authTokenData=this.tokenService.createAuthToken({id:refreshTokenData.id,password:"",secretToken:refreshTokenData.secretToken}).authToken;const newRefreshTokenData=this.tokenService.createRefreshToken({id:refreshTokenData.id,password:"",secretToken:refreshTokenData.secretToken});return{session:authTokenData,refreshToken:newRefreshTokenData.refreshToken}}validateToken(token){const decrypted=this.utilService.aesDecrypt(token);if(!decrypted)return{result:false,email:null,password:null};return{result:true,email:decrypted.email,password:decrypted.password}}constructor(tokenService,mailService,utilService,userRepository,accountRepository,cacheService){this.tokenService=tokenService;this.mailService=mailService;this.utilService=utilService;this.userRepository=userRepository;this.accountRepository=accountRepository;this.cacheService=cacheService;this.RESETPASSWORDTIMEOUT=18e5;this.SECRETKEY="sYzB9UTkuLQ0d1DNPZabC4Q29iJ32xGX";this.INITVECTOR="3dMYNoQo2CSYDpSD";this.SECRETSTRING="6H2su82wAS85KowZ"}};AuthService=_ts_decorate([(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _services.TokenService==="undefined"?Object:_services.TokenService,typeof _mailservice.MailService==="undefined"?Object:_mailservice.MailService,typeof _services.UtilService==="undefined"?Object:_services.UtilService,typeof _userrepository.UserRepository==="undefined"?Object:_userrepository.UserRepository,typeof _accountrepository.AccountRepository==="undefined"?Object:_accountrepository.AccountRepository,typeof _cacheservice.CacheService==="undefined"?Object:_cacheservice.CacheService])],AuthService);
//# sourceMappingURL=auth.service.js.map