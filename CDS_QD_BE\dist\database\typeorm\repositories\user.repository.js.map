{"version": 3, "sources": ["../../../../src/database/typeorm/repositories/user.repository.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\nimport { DataSource, Repository } from 'typeorm';\nimport { UserEntity } from '~/database/typeorm/entities/user.entity';\n\n@Injectable()\nexport class UserRepository extends Repository<UserEntity> {\n    constructor(private dataSource: DataSource) {\n        super(UserEntity, dataSource.createEntityManager());\n    }\n\n    findOneUserWithAllRelationsById = (id: number) => {\n        return this.findOne({\n            where: { id: id },\n            relations: ['userRoles', 'userRoles.role', 'avatar', 'account'],\n        });\n    };\n\n    // Find user with all roles (only multiple roles now)\n    findOneUserWithAllRolesById = (id: number) => {\n        return this.findOne({\n            where: { id: id },\n            relations: ['userRoles', 'userRoles.role', 'avatar', 'account'],\n        });\n    };\n\n    findOneWithRalations = ({ where, relations }: { where: any; relations: string[] }) => {\n        const builder = this.createQueryBuilder('entity');\n        if (where) {\n            builder.where(where);\n        }\n\n        if (relations.length) {\n            relations.forEach((relation) => {\n                builder.leftJoinAndMapOne(`entity.${relation}`, `entity.${relation}`, relation, `${relation}.id = entity.${relation}Id`);\n            });\n        }\n\n        return builder.getOne();\n    };\n\n    // Find user by account ID with all roles\n    findOneUserWithRolesByAccountId = (accountId: number) => {\n        return this.findOne({\n            where: { accountId },\n            relations: ['userRoles', 'userRoles.role'],\n        });\n    };\n\n    // Get all roles for a user (only multiple roles now)\n    async getAllUserRoles(userId: number) {\n        const user = await this.findOne({\n            where: { id: userId },\n            relations: ['userRoles', 'userRoles.role'],\n        });\n\n        if (!user) return [];\n\n        const roles = [];\n\n        // Add multiple roles\n        if (user.userRoles && user.userRoles.length > 0) {\n            user.userRoles.forEach((userRole) => {\n                if (userRole.role) {\n                    roles.push(userRole.role);\n                }\n            });\n        }\n\n        return roles;\n    }\n}\n"], "names": ["UserRepository", "Repository", "getAllUserRoles", "userId", "user", "findOne", "where", "id", "relations", "roles", "userRoles", "length", "for<PERSON>ach", "userRole", "role", "push", "constructor", "dataSource", "UserEntity", "createEntityManager", "findOneUserWithAllRelationsById", "findOneUserWithAllRolesById", "findOneWithRalations", "builder", "createQueryBuilder", "relation", "leftJoinAndMapOne", "getOne", "findOneUserWithRolesByAccountId", "accountId"], "mappings": "oGAKaA,wDAAAA,wCALc,yCACY,qCACZ,wkBAGpB,IAAA,AAAMA,eAAN,MAAMA,uBAAuBC,mBAAU,CA4C1C,MAAMC,gBAAgBC,MAAc,CAAE,CAClC,MAAMC,KAAO,MAAM,IAAI,CAACC,OAAO,CAAC,CAC5BC,MAAO,CAAEC,GAAIJ,MAAO,EACpBK,UAAW,CAAC,YAAa,iBAAiB,AAC9C,GAEA,GAAI,CAACJ,KAAM,MAAO,EAAE,CAEpB,MAAMK,MAAQ,EAAE,CAGhB,GAAIL,KAAKM,SAAS,EAAIN,KAAKM,SAAS,CAACC,MAAM,CAAG,EAAG,CAC7CP,KAAKM,SAAS,CAACE,OAAO,CAAC,AAACC,WACpB,GAAIA,SAASC,IAAI,CAAE,CACfL,MAAMM,IAAI,CAACF,SAASC,IAAI,CAC5B,CACJ,EACJ,CAEA,OAAOL,KACX,CA/DAO,YAAY,AAAQC,UAAsB,CAAE,CACxC,KAAK,CAACC,sBAAU,CAAED,WAAWE,mBAAmB,SADhCF,WAAAA,gBAIpBG,gCAAkC,AAACb,KAC/B,OAAO,IAAI,CAACF,OAAO,CAAC,CAChBC,MAAO,CAAEC,GAAIA,EAAG,EAChBC,UAAW,CAAC,YAAa,iBAAkB,SAAU,UAAU,AACnE,EACJ,OAGAa,4BAA8B,AAACd,KAC3B,OAAO,IAAI,CAACF,OAAO,CAAC,CAChBC,MAAO,CAAEC,GAAIA,EAAG,EAChBC,UAAW,CAAC,YAAa,iBAAkB,SAAU,UAAU,AACnE,EACJ,OAEAc,qBAAuB,CAAC,CAAEhB,KAAK,CAAEE,SAAS,CAAuC,IAC7E,MAAMe,QAAU,IAAI,CAACC,kBAAkB,CAAC,UACxC,GAAIlB,MAAO,CACPiB,QAAQjB,KAAK,CAACA,MAClB,CAEA,GAAIE,UAAUG,MAAM,CAAE,CAClBH,UAAUI,OAAO,CAAC,AAACa,WACfF,QAAQG,iBAAiB,CAAC,CAAC,OAAO,EAAED,SAAS,CAAC,CAAE,CAAC,OAAO,EAAEA,SAAS,CAAC,CAAEA,SAAU,CAAC,EAAEA,SAAS,aAAa,EAAEA,SAAS,EAAE,CAAC,CAC3H,EACJ,CAEA,OAAOF,QAAQI,MAAM,EACzB,OAGAC,gCAAkC,AAACC,YAC/B,OAAO,IAAI,CAACxB,OAAO,CAAC,CAChBC,MAAO,CAAEuB,SAAU,EACnBrB,UAAW,CAAC,YAAa,iBAAiB,AAC9C,EACJ,CAtCA,CA8DJ"}