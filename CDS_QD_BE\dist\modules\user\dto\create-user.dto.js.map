{"version": 3, "sources": ["../../../../src/modules/user/dto/create-user.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\nimport { Transform } from 'class-transformer';\nimport { IsEmail, IsNotEmpty, IsOptional, IsArray, IsNumber } from 'class-validator';\n\nexport class CreateUserDto {\n    @ApiProperty()\n    @IsNotEmpty({ message: 'Tên đăng nhập không được để trống' })\n    username: string;\n\n    @ApiProperty()\n    @IsNotEmpty({ message: '<PERSON><PERSON>t khẩu không được để trống' })\n    password: string;\n\n    @ApiProperty()\n    @IsNotEmpty({ message: 'Email không được để trống' })\n    @IsEmail({}, { message: '<PERSON>ail không hợp lệ' })\n    email: string;\n\n    @ApiProperty()\n    @IsOptional()\n    roleId: number;\n\n    @ApiProperty({\n        description: 'Array of role IDs for multiple roles (optional)',\n        example: [1, 2, 3],\n        type: [Number],\n        required: false,\n    })\n    @IsOptional()\n    @IsArray({ message: 'Role IDs phải là một mảng' })\n    @IsNumber({}, { each: true, message: 'Mỗi role ID phải là một số' })\n    roleIds?: number[];\n\n    @ApiProperty()\n    @IsOptional()\n    departmentId: number;\n\n    @ApiProperty()\n    @IsOptional()\n    fullName: string;\n\n    @ApiProperty()\n    @IsOptional()\n    areaCode: string;\n\n    @ApiProperty()\n    @IsOptional()\n    phone: string;\n\n    @ApiProperty()\n    @IsOptional()\n    address: string;\n\n    @ApiProperty()\n    @IsOptional()\n    birthday: string;\n\n    @ApiProperty()\n    @IsOptional()\n    @Transform(({ value }) => value?.toLowerCase())\n    gender: string;\n}\n"], "names": ["CreateUserDto", "message", "description", "example", "type", "Number", "required", "each", "value", "toLowerCase"], "mappings": "oGAIaA,uDAAAA,wCAJe,mDACF,mDACyC,gkBAE5D,IAAA,AAAMA,cAAN,MAAMA,cAyDb,2EAvDkBC,QAAS,8LAITA,QAAS,yLAITA,QAAS,8DACRA,QAAS,kSAQpBC,YAAa,kDACbC,QAAS,CAAC,EAAG,EAAG,EAAE,CAClBC,KAAM,CAACC,OAAO,CACdC,SAAU,sEAGHL,QAAS,+DACJM,KAAM,KAAMN,QAAS,6mCA6BzB,CAAEO,KAAK,CAAE,GAAKA,OAAOC"}