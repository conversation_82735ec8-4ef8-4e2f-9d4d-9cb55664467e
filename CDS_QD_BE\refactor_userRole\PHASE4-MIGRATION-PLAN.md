# Phase 4: Migration to Unified Multiple Roles System

## Mục tiêu
Lo<PERSON>i bỏ hoàn toàn single role system và thống nhất chỉ sử dụng multiple roles system với trường `roles` (array) duy nhất.

## Kế hoạch Migration

### Step 1: Data Migration (Ưu tiên cao nhất)
- [x] Tạo migration script để chuyển dữ liệu từ `users.role_id` sang `users_roles`
- [x] Backup dữ liệu trước khi migration
- [x] Validate data integrity sau migration

### Step 2: Backend Services Update
- [ ] Cập nhật AuthService.login() để chỉ trả về `roles` array
- [ ] Cập nhật PermissionGuard để sử dụng `roles[0]` làm primary role
- [ ] Cập nhật UserService để loại bỏ single role logic
- [ ] Cập nhật UserRepository để chỉ load multiple roles

### Step 3: Database Schema Update
- [ ] Deprecate `users.role_id` column (không xóa ngay để rollback)
- [ ] Cập nhật entities để không sử dụng single role
- [ ] Cập nhật seeds để sử dụng multiple roles

### Step 4: API Response Standardization
- [ ] Cập nhật tất cả API responses để chỉ trả về `roles`
- [ ] Thêm backward compatibility layer nếu cần
- [ ] Cập nhật API documentation

### Step 5: Testing & Validation
- [ ] Test migration script với data thực
- [ ] Test tất cả API endpoints
- [ ] Validate permission system hoạt động đúng
- [ ] Test edge cases

### Step 6: Frontend Compatibility (Tùy chọn)
- [ ] Thêm compatibility layer: `role = roles[0]` nếu frontend cần
- [ ] Cập nhật frontend để sử dụng `roles` array
- [ ] Remove compatibility layer sau khi frontend updated

## Chi tiết thực hiện

### 1. Data Migration Strategy

```sql
-- Step 1: Migrate existing single roles to multiple roles
INSERT INTO users_roles (user_id, role_id, is_primary)
SELECT id, role_id, true
FROM users 
WHERE role_id IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM users_roles ur 
    WHERE ur.user_id = users.id AND ur.role_id = users.role_id
);

-- Step 2: Validate migration
SELECT 
    u.id,
    u.role_id as old_single_role,
    array_agg(ur.role_id) as new_multiple_roles
FROM users u
LEFT JOIN users_roles ur ON u.id = ur.user_id
GROUP BY u.id, u.role_id
HAVING u.role_id IS NOT NULL;
```

### 2. Backend Architecture Changes

#### Before (Current):
```typescript
// User có cả single role và multiple roles
user = {
    roleId: 1,
    role: { id: 1, name: 'Admin' },
    userRoles: [
        { role: { id: 2, name: 'Editor' } }
    ]
}

// Login response
{
    role: { id: 1, name: 'Admin' },      // Single role
    roles: [Admin, Editor]               // Multiple roles
}
```

#### After (Target):
```typescript
// User chỉ có multiple roles
user = {
    roleId: null,           // Deprecated
    role: null,             // Deprecated  
    userRoles: [
        { role: { id: 1, name: 'Admin' }, isPrimary: true },
        { role: { id: 2, name: 'Editor' }, isPrimary: false }
    ]
}

// Login response
{
    roles: [Admin, Editor]  // Chỉ có multiple roles, roles[0] là primary
}
```

### 3. Backward Compatibility Strategy

```typescript
// Compatibility layer cho frontend cũ
interface LoginResponse {
    roles: RoleEntity[];
    // Computed property for backward compatibility
    get role(): RoleEntity | null {
        return this.roles?.[0] || null;
    }
}
```

## Lợi ích của Migration

### ✅ Đơn giản hóa
- Chỉ một cách duy nhất để quản lý roles
- Loại bỏ logic phức tạp giữa single/multiple roles
- Code dễ maintain và extend

### ✅ Linh hoạt
- User có thể có nhiều roles tùy ý
- Dễ dàng thêm/xóa roles
- Primary role có thể thay đổi

### ✅ Nhất quán
- Tất cả users đều sử dụng cùng một system
- API responses nhất quán
- Database schema đơn giản hơn

## Rủi ro và Mitigation

### ⚠️ Rủi ro
1. **Data loss**: Migration script có thể fail
2. **Downtime**: Cần update database và code
3. **Frontend breaking**: Nếu frontend depend vào `role` field
4. **Performance**: Query multiple roles có thể chậm hơn

### 🛡️ Mitigation
1. **Backup**: Full database backup trước migration
2. **Rollback plan**: Giữ `role_id` column để rollback
3. **Compatibility layer**: Map `roles[0]` thành `role`
4. **Testing**: Comprehensive testing trước production
5. **Gradual rollout**: Deploy từng service một

## Timeline

### Week 1: Preparation
- [x] Tạo migration scripts
- [x] Setup testing environment
- [x] Backup production data

### Week 2: Backend Migration
- [ ] Migrate data
- [ ] Update backend services
- [ ] Test thoroughly

### Week 3: API Standardization
- [ ] Update API responses
- [ ] Add compatibility layer
- [ ] Update documentation

### Week 4: Validation & Cleanup
- [ ] Production testing
- [ ] Performance monitoring
- [ ] Remove deprecated code (optional)

## Success Criteria

1. ✅ Tất cả users có data trong `users_roles` table
2. ✅ Login response chỉ có `roles` field
3. ✅ Permission system hoạt động đúng với multiple roles
4. ✅ Không có breaking changes cho frontend
5. ✅ Performance không giảm đáng kể
6. ✅ Tất cả tests pass

## Rollback Plan

Nếu có vấn đề, có thể rollback bằng cách:
1. Restore database từ backup
2. Revert code changes
3. Sử dụng lại single role system
4. `users.role_id` column vẫn còn để fallback

---

**Status: 🚧 Ready to Execute**
**Risk Level: 🟡 Medium (có mitigation plans)**
**Impact: 🔴 High (architectural change)**
