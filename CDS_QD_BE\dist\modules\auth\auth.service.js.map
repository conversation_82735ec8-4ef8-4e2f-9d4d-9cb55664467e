{"version": 3, "sources": ["../../../src/modules/auth/auth.service.ts"], "sourcesContent": ["import { Injectable, InternalServerErrorException, UnauthorizedException } from '@nestjs/common';\nimport { TokenService, UtilService } from '@shared/services';\nimport { USER_STATUS } from '~/common/enums/enum';\nimport { AccountRepository } from '~/database/typeorm/repositories/account.repository';\nimport { UserRepository } from '~/database/typeorm/repositories/user.repository';\nimport { MailService } from '~/modules/mail/mail.service';\nimport { CacheService } from '~/shared/services/cache.service';\n\n@Injectable()\nexport class AuthService {\n    private readonly RESETPASSWORDTIMEOUT = 1800000; // miliseconds (30 mins)\n    private readonly SECRETKEY = 'sYzB9UTkuLQ0d1DNPZabC4Q29iJ32xGX';\n    private readonly INITVECTOR = '3dMYNoQo2CSYDpSD';\n    private readonly SECRETSTRING = '6H2su82wAS85KowZ';\n\n    constructor(\n        private readonly tokenService: TokenService,\n        private readonly mailService: MailService,\n        private readonly utilService: UtilService,\n        private readonly userRepository: UserRepository,\n        private readonly accountRepository: AccountRepository,\n        private readonly cacheService: CacheService,\n    ) {}\n\n    public async login(data: { username: string; password: string }) {\n        try {\n            const account = await this.accountRepository.findOne({\n                select: ['id', 'username', 'password', 'secretToken', 'isActive'],\n                where: {\n                    username: data.username,\n                },\n            });\n\n            if (!account) {\n                throw new UnauthorizedException('Wrong username or password');\n            }\n\n            if (!account.isActive) {\n                throw new UnauthorizedException('User disabled');\n            }\n\n            if (!this.tokenService.isPasswordCorrect(data.password, account.password)) {\n                throw new UnauthorizedException('Wrong username or password');\n            }\n\n            const secretToken = this.utilService.generateString();\n            const tokenData = this.tokenService.createAuthToken({\n                id: account.id,\n                password: '', // Giá trị placeholder, không sử dụng trong tokenService mới\n                secretToken,\n            });\n            const refreshTokenData = this.tokenService.createRefreshToken({\n                id: account.id,\n                password: '', // Giá trị placeholder, không sử dụng trong tokenService mới\n                secretToken,\n            });\n            this.accountRepository.update(account.id, { secretToken });\n\n            const user = await this.userRepository.findOneUserWithRolesByAccountId(account.id);\n            if (!user || user.status !== USER_STATUS.ACTIVE) throw new UnauthorizedException('User not found');\n\n            // Get all roles for the user (only multiple roles now)\n            const roles = await this.userRepository.getAllUserRoles(user.id);\n\n            this.cacheService.delete(`account:${account.id}`);\n            return {\n                result: true,\n                message: 'Login successfully',\n                data: {\n                    id: account.id,\n                    session: tokenData.authToken,\n                    expired: tokenData.authTokenExpiresIn,\n                    refreshToken: refreshTokenData.refreshToken,\n                    roles: roles, // Only multiple roles - roles[0] is primary role\n                },\n            };\n        } catch (err) {\n            throw new UnauthorizedException('Login error');\n        }\n    }\n\n    public async logout(data: { session: string }) {\n        const user = await this.tokenService.verifyAuthToken({ authToken: data.session });\n        if (user.id) {\n            const accountId = (await this.userRepository.findOneBy({ id: +user.id })).accountId;\n            if (accountId) {\n                this.accountRepository.update(accountId, { secretToken: null });\n                this.cacheService.delete(`account:${accountId}`);\n            }\n        }\n\n        return {\n            result: true,\n            message: 'Success',\n            data: null,\n        };\n    }\n\n    public async forgotPassword(data: { email: string }) {\n        try {\n            if (!this.utilService.validateEmail(data.email)) {\n                return {\n                    result: false,\n                    message: 'Email is invalid',\n                    data: null,\n                };\n            }\n\n            const user = await this.userRepository.findOne({\n                select: ['email', 'hoTen', 'status', 'account'],\n                where: { email: data.email },\n                relations: ['account'],\n            });\n            if (!user) {\n                return {\n                    result: false,\n                    message: 'User not found',\n                    data: null,\n                };\n            }\n\n            if (user.status === USER_STATUS.DISABLED) {\n                return {\n                    result: false,\n                    message: 'User disabled',\n                    data: {\n                        is_active: false,\n                    },\n                };\n            }\n\n            const encrypted = this.utilService.aesEncrypt({ email: user.email, password: user.account.password }, this.RESETPASSWORDTIMEOUT);\n            const link = `${process.env.FE_URL}/reset-password?token=${encrypted}`;\n            // gửi mail link reset password cho user\n            this.mailService.sendForgotPassword({\n                emailTo: user.email,\n                subject: 'Reset your password',\n                name: user.hoTen,\n                link: link,\n            });\n\n            return {\n                result: true,\n                message: 'Reset-password link has been sent to your email',\n                data: null,\n            };\n        } catch (err) {\n            throw new InternalServerErrorException({\n                result: false,\n                message: 'Forgot password error',\n                data: null,\n                statusCode: 500,\n            });\n        }\n    }\n\n    public async resetPassword(data: { token: string; password: string }) {\n        try {\n            const validateToken = this.validateToken(data.token);\n            if (!validateToken.result) {\n                return {\n                    result: false,\n                    message: 'Token invalid',\n                    data: null,\n                };\n            }\n\n            const email = validateToken.email;\n            const password = validateToken.password;\n            const user = await this.userRepository.findOne({\n                select: ['id', 'account'],\n                where: { email: email },\n                relations: ['account'],\n            });\n            if (!user) {\n                return {\n                    result: false,\n                    message: 'User not found',\n                    data: null,\n                };\n            }\n\n            if (user.account.password !== password) {\n                return {\n                    result: false,\n                    message: 'Token expired',\n                    data: null,\n                };\n            }\n\n            const { salt, hash } = this.tokenService.hashPassword(data.password);\n            const res = await this.accountRepository.update(user.account.id, {\n                password: hash,\n                salt,\n            });\n\n            return {\n                result: res.affected > 0,\n                message: res.affected > 0 ? 'Password reset successfully' : 'Cannot reset password',\n                data: null,\n            };\n        } catch (err) {\n            throw new InternalServerErrorException({\n                result: false,\n                message: 'Reset password error',\n                data: null,\n                statusCode: 500,\n            });\n        }\n    }\n\n    public async renewAuthToken(data: { refreshToken }) {\n        const refreshTokenData = this.tokenService.verifyRefreshToken({ refreshToken: data.refreshToken });\n        if (!refreshTokenData) {\n            return {\n                session: null,\n                refreshToken: null,\n            };\n        }\n\n        // Lấy thông tin account để tạo token mới\n        const account = await this.accountRepository.findOne({\n            select: ['id', 'secretToken'],\n            where: { id: refreshTokenData.id },\n        });\n\n        if (!account) {\n            return {\n                session: null,\n                refreshToken: null,\n            };\n        }\n\n        const authTokenData = this.tokenService.createAuthToken({\n            id: refreshTokenData.id,\n            password: '', // Giá trị placeholder, không sử dụng trong tokenService mới\n            secretToken: refreshTokenData.secretToken,\n        }).authToken;\n\n        const newRefreshTokenData = this.tokenService.createRefreshToken({\n            id: refreshTokenData.id,\n            password: '', // Giá trị placeholder, không sử dụng trong tokenService mới\n            secretToken: refreshTokenData.secretToken,\n        });\n\n        return {\n            session: authTokenData,\n            refreshToken: newRefreshTokenData.refreshToken,\n        };\n    }\n\n    private validateToken(token: string) {\n        const decrypted = this.utilService.aesDecrypt(token);\n        if (!decrypted) return { result: false, email: null, password: null };\n        return {\n            result: true,\n            email: decrypted.email,\n            password: decrypted.password,\n        };\n    }\n}\n"], "names": ["AuthService", "login", "data", "account", "accountRepository", "findOne", "select", "where", "username", "UnauthorizedException", "isActive", "tokenService", "isPasswordCorrect", "password", "secretToken", "utilService", "generateString", "tokenData", "createAuthToken", "id", "refreshTokenData", "createRefreshToken", "update", "user", "userRepository", "findOneUserWithRolesByAccountId", "status", "USER_STATUS", "ACTIVE", "roles", "getAllUserRoles", "cacheService", "delete", "result", "message", "session", "authToken", "expired", "authTokenExpiresIn", "refreshToken", "err", "logout", "verifyAuthToken", "accountId", "findOneBy", "forgotPassword", "validateEmail", "email", "relations", "DISABLED", "is_active", "encrypted", "aesEncrypt", "RESETPASSWORDTIMEOUT", "link", "process", "env", "FE_URL", "mailService", "sendForgotPassword", "emailTo", "subject", "name", "hoTen", "InternalServerErrorException", "statusCode", "resetPassword", "validateToken", "token", "salt", "hash", "hashPassword", "res", "affected", "renewAuthToken", "verifyRefreshToken", "authTokenData", "newRefreshTokenData", "decrypted", "aesDecrypt", "constructor", "SECRETKEY", "INITVECTOR", "SECRETSTRING"], "mappings": "oGASaA,qDAAAA,qCATmE,0CACtC,6CACd,4DACM,wFACH,kFACH,oDACC,olBAGtB,IAAA,AAAMA,YAAN,MAAMA,YAeT,MAAaC,MAAMC,IAA4C,CAAE,CAC7D,GAAI,CACA,MAAMC,QAAU,MAAM,IAAI,CAACC,iBAAiB,CAACC,OAAO,CAAC,CACjDC,OAAQ,CAAC,KAAM,WAAY,WAAY,cAAe,WAAW,CACjEC,MAAO,CACHC,SAAUN,KAAKM,QAAQ,AAC3B,CACJ,GAEA,GAAI,CAACL,QAAS,CACV,MAAM,IAAIM,6BAAqB,CAAC,6BACpC,CAEA,GAAI,CAACN,QAAQO,QAAQ,CAAE,CACnB,MAAM,IAAID,6BAAqB,CAAC,gBACpC,CAEA,GAAI,CAAC,IAAI,CAACE,YAAY,CAACC,iBAAiB,CAACV,KAAKW,QAAQ,CAAEV,QAAQU,QAAQ,EAAG,CACvE,MAAM,IAAIJ,6BAAqB,CAAC,6BACpC,CAEA,MAAMK,YAAc,IAAI,CAACC,WAAW,CAACC,cAAc,GACnD,MAAMC,UAAY,IAAI,CAACN,YAAY,CAACO,eAAe,CAAC,CAChDC,GAAIhB,QAAQgB,EAAE,CACdN,SAAU,GACVC,WACJ,GACA,MAAMM,iBAAmB,IAAI,CAACT,YAAY,CAACU,kBAAkB,CAAC,CAC1DF,GAAIhB,QAAQgB,EAAE,CACdN,SAAU,GACVC,WACJ,GACA,IAAI,CAACV,iBAAiB,CAACkB,MAAM,CAACnB,QAAQgB,EAAE,CAAE,CAAEL,WAAY,GAExD,MAAMS,KAAO,MAAM,IAAI,CAACC,cAAc,CAACC,+BAA+B,CAACtB,QAAQgB,EAAE,EACjF,GAAI,CAACI,MAAQA,KAAKG,MAAM,GAAKC,iBAAW,CAACC,MAAM,CAAE,MAAM,IAAInB,6BAAqB,CAAC,kBAGjF,MAAMoB,MAAQ,MAAM,IAAI,CAACL,cAAc,CAACM,eAAe,CAACP,KAAKJ,EAAE,EAE/D,IAAI,CAACY,YAAY,CAACC,MAAM,CAAC,CAAC,QAAQ,EAAE7B,QAAQgB,EAAE,CAAC,CAAC,EAChD,MAAO,CACHc,OAAQ,KACRC,QAAS,qBACThC,KAAM,CACFiB,GAAIhB,QAAQgB,EAAE,CACdgB,QAASlB,UAAUmB,SAAS,CAC5BC,QAASpB,UAAUqB,kBAAkB,CACrCC,aAAcnB,iBAAiBmB,YAAY,CAC3CV,MAAOA,KACX,CACJ,CACJ,CAAE,MAAOW,IAAK,CACV,MAAM,IAAI/B,6BAAqB,CAAC,cACpC,CACJ,CAEA,MAAagC,OAAOvC,IAAyB,CAAE,CAC3C,MAAMqB,KAAO,MAAM,IAAI,CAACZ,YAAY,CAAC+B,eAAe,CAAC,CAAEN,UAAWlC,KAAKiC,OAAO,AAAC,GAC/E,GAAIZ,KAAKJ,EAAE,CAAE,CACT,MAAMwB,UAAY,AAAC,CAAA,MAAM,IAAI,CAACnB,cAAc,CAACoB,SAAS,CAAC,CAAEzB,GAAI,CAACI,KAAKJ,EAAE,AAAC,EAAC,EAAGwB,SAAS,CACnF,GAAIA,UAAW,CACX,IAAI,CAACvC,iBAAiB,CAACkB,MAAM,CAACqB,UAAW,CAAE7B,YAAa,IAAK,GAC7D,IAAI,CAACiB,YAAY,CAACC,MAAM,CAAC,CAAC,QAAQ,EAAEW,UAAU,CAAC,CACnD,CACJ,CAEA,MAAO,CACHV,OAAQ,KACRC,QAAS,UACThC,KAAM,IACV,CACJ,CAEA,MAAa2C,eAAe3C,IAAuB,CAAE,CACjD,GAAI,CACA,GAAI,CAAC,IAAI,CAACa,WAAW,CAAC+B,aAAa,CAAC5C,KAAK6C,KAAK,EAAG,CAC7C,MAAO,CACHd,OAAQ,MACRC,QAAS,mBACThC,KAAM,IACV,CACJ,CAEA,MAAMqB,KAAO,MAAM,IAAI,CAACC,cAAc,CAACnB,OAAO,CAAC,CAC3CC,OAAQ,CAAC,QAAS,QAAS,SAAU,UAAU,CAC/CC,MAAO,CAAEwC,MAAO7C,KAAK6C,KAAK,AAAC,EAC3BC,UAAW,CAAC,UAAU,AAC1B,GACA,GAAI,CAACzB,KAAM,CACP,MAAO,CACHU,OAAQ,MACRC,QAAS,iBACThC,KAAM,IACV,CACJ,CAEA,GAAIqB,KAAKG,MAAM,GAAKC,iBAAW,CAACsB,QAAQ,CAAE,CACtC,MAAO,CACHhB,OAAQ,MACRC,QAAS,gBACThC,KAAM,CACFgD,UAAW,KACf,CACJ,CACJ,CAEA,MAAMC,UAAY,IAAI,CAACpC,WAAW,CAACqC,UAAU,CAAC,CAAEL,MAAOxB,KAAKwB,KAAK,CAAElC,SAAUU,KAAKpB,OAAO,CAACU,QAAQ,AAAC,EAAG,IAAI,CAACwC,oBAAoB,EAC/H,MAAMC,KAAO,CAAC,EAAEC,QAAQC,GAAG,CAACC,MAAM,CAAC,sBAAsB,EAAEN,UAAU,CAAC,CAEtE,IAAI,CAACO,WAAW,CAACC,kBAAkB,CAAC,CAChCC,QAASrC,KAAKwB,KAAK,CACnBc,QAAS,sBACTC,KAAMvC,KAAKwC,KAAK,CAChBT,KAAMA,IACV,GAEA,MAAO,CACHrB,OAAQ,KACRC,QAAS,kDACThC,KAAM,IACV,CACJ,CAAE,MAAOsC,IAAK,CACV,MAAM,IAAIwB,oCAA4B,CAAC,CACnC/B,OAAQ,MACRC,QAAS,wBACThC,KAAM,KACN+D,WAAY,GAChB,EACJ,CACJ,CAEA,MAAaC,cAAchE,IAAyC,CAAE,CAClE,GAAI,CACA,MAAMiE,cAAgB,IAAI,CAACA,aAAa,CAACjE,KAAKkE,KAAK,EACnD,GAAI,CAACD,cAAclC,MAAM,CAAE,CACvB,MAAO,CACHA,OAAQ,MACRC,QAAS,gBACThC,KAAM,IACV,CACJ,CAEA,MAAM6C,MAAQoB,cAAcpB,KAAK,CACjC,MAAMlC,SAAWsD,cAActD,QAAQ,CACvC,MAAMU,KAAO,MAAM,IAAI,CAACC,cAAc,CAACnB,OAAO,CAAC,CAC3CC,OAAQ,CAAC,KAAM,UAAU,CACzBC,MAAO,CAAEwC,MAAOA,KAAM,EACtBC,UAAW,CAAC,UAAU,AAC1B,GACA,GAAI,CAACzB,KAAM,CACP,MAAO,CACHU,OAAQ,MACRC,QAAS,iBACThC,KAAM,IACV,CACJ,CAEA,GAAIqB,KAAKpB,OAAO,CAACU,QAAQ,GAAKA,SAAU,CACpC,MAAO,CACHoB,OAAQ,MACRC,QAAS,gBACThC,KAAM,IACV,CACJ,CAEA,KAAM,CAAEmE,IAAI,CAAEC,IAAI,CAAE,CAAG,IAAI,CAAC3D,YAAY,CAAC4D,YAAY,CAACrE,KAAKW,QAAQ,EACnE,MAAM2D,IAAM,MAAM,IAAI,CAACpE,iBAAiB,CAACkB,MAAM,CAACC,KAAKpB,OAAO,CAACgB,EAAE,CAAE,CAC7DN,SAAUyD,KACVD,IACJ,GAEA,MAAO,CACHpC,OAAQuC,IAAIC,QAAQ,CAAG,EACvBvC,QAASsC,IAAIC,QAAQ,CAAG,EAAI,8BAAgC,wBAC5DvE,KAAM,IACV,CACJ,CAAE,MAAOsC,IAAK,CACV,MAAM,IAAIwB,oCAA4B,CAAC,CACnC/B,OAAQ,MACRC,QAAS,uBACThC,KAAM,KACN+D,WAAY,GAChB,EACJ,CACJ,CAEA,MAAaS,eAAexE,IAAsB,CAAE,CAChD,MAAMkB,iBAAmB,IAAI,CAACT,YAAY,CAACgE,kBAAkB,CAAC,CAAEpC,aAAcrC,KAAKqC,YAAY,AAAC,GAChG,GAAI,CAACnB,iBAAkB,CACnB,MAAO,CACHe,QAAS,KACTI,aAAc,IAClB,CACJ,CAGA,MAAMpC,QAAU,MAAM,IAAI,CAACC,iBAAiB,CAACC,OAAO,CAAC,CACjDC,OAAQ,CAAC,KAAM,cAAc,CAC7BC,MAAO,CAAEY,GAAIC,iBAAiBD,EAAE,AAAC,CACrC,GAEA,GAAI,CAAChB,QAAS,CACV,MAAO,CACHgC,QAAS,KACTI,aAAc,IAClB,CACJ,CAEA,MAAMqC,cAAgB,IAAI,CAACjE,YAAY,CAACO,eAAe,CAAC,CACpDC,GAAIC,iBAAiBD,EAAE,CACvBN,SAAU,GACVC,YAAaM,iBAAiBN,WAAW,AAC7C,GAAGsB,SAAS,CAEZ,MAAMyC,oBAAsB,IAAI,CAAClE,YAAY,CAACU,kBAAkB,CAAC,CAC7DF,GAAIC,iBAAiBD,EAAE,CACvBN,SAAU,GACVC,YAAaM,iBAAiBN,WAAW,AAC7C,GAEA,MAAO,CACHqB,QAASyC,cACTrC,aAAcsC,oBAAoBtC,YAAY,AAClD,CACJ,CAEA,AAAQ4B,cAAcC,KAAa,CAAE,CACjC,MAAMU,UAAY,IAAI,CAAC/D,WAAW,CAACgE,UAAU,CAACX,OAC9C,GAAI,CAACU,UAAW,MAAO,CAAE7C,OAAQ,MAAOc,MAAO,KAAMlC,SAAU,IAAK,EACpE,MAAO,CACHoB,OAAQ,KACRc,MAAO+B,UAAU/B,KAAK,CACtBlC,SAAUiE,UAAUjE,QAAQ,AAChC,CACJ,CApPAmE,YACI,AAAiBrE,YAA0B,CAC3C,AAAiB+C,WAAwB,CACzC,AAAiB3C,WAAwB,CACzC,AAAiBS,cAA8B,CAC/C,AAAiBpB,iBAAoC,CACrD,AAAiB2B,YAA0B,CAC7C,MANmBpB,aAAAA,kBACA+C,YAAAA,iBACA3C,YAAAA,iBACAS,eAAAA,oBACApB,kBAAAA,uBACA2B,aAAAA,kBAXJsB,qBAAuB,UACvB4B,UAAY,wCACZC,WAAa,wBACbC,aAAe,kBAS7B,CA8OP"}