{"version": 3, "sources": ["../../../../src/modules/user/dto/assign-roles.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\nimport { IsArray, IsNotEmpty, IsNumber } from 'class-validator';\n\nexport class AssignRolesDto {\n    @ApiProperty({ \n        description: 'Array of role IDs to assign to user',\n        example: [1, 2, 3],\n        type: [Number]\n    })\n    @IsArray({ message: 'Role IDs phải là một mảng' })\n    @IsNotEmpty({ message: 'Role IDs không được để trống' })\n    @IsNumber({}, { each: true, message: 'Mỗi role ID phải là một số' })\n    roleIds: number[];\n}\n\nexport class RemoveRoleDto {\n    @ApiProperty({ \n        description: 'Role ID to remove from user',\n        example: 1\n    })\n    @IsNotEmpty({ message: 'Role ID không được để trống' })\n    @IsNumber({}, { message: 'Role ID phải là một số' })\n    roleId: number;\n}\n"], "names": ["AssignRolesDto", "RemoveRoleDto", "description", "example", "type", "Number", "message", "each"], "mappings": "mPAGaA,wBAAAA,oBAYAC,uBAAAA,wCAfe,iDACkB,gkBAEvC,IAAA,AAAMD,eAAN,MAAMA,eAUb,0CARQE,YAAa,sCACbC,QAAS,CAAC,EAAG,EAAG,EAAE,CAClBC,KAAM,CAACC,OAAO,gCAEPC,QAAS,8DACNA,QAAS,kEACPC,KAAM,KAAMD,QAAS,6GAIlC,IAAA,AAAML,cAAN,MAAMA,cAQb,0CANQC,YAAa,8BACbC,QAAS,oCAECG,QAAS,iEACPA,QAAS"}