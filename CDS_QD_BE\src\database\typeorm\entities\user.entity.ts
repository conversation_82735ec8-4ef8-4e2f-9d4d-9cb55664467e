import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, OneToOne, PrimaryGeneratedColumn, Relation, ManyToOne } from 'typeorm';
import { AbstractEntity } from '~/database/typeorm/entities/abstract.entity';
import { AccountEntity } from '~/database/typeorm/entities/account.entity';
import { GiaTriDanhMucEntity } from '~/database/typeorm/entities/giaTriDanhMuc.entity';
import { DuLieuTepTinEntity } from '~/database/typeorm/entities/duLieuTepTin.entity';
import { QuanNhanEntity } from '~/database/typeorm/entities/quanNhan.entity';
import { UsersRoleEntity } from './usersRole.entity';
import { USER_STATUS } from '~/common/enums/enum';

@Entity({ name: 'users' })
export class UserEntity extends AbstractEntity {
    @PrimaryGeneratedColumn('increment', { name: 'id', type: 'int', unsigned: true })
    id: number;

    @Column({ name: 'account_id', type: 'bigint', nullable: false })
    accountId: number;

    @Column({ name: 'avatar_id', type: 'uuid', nullable: true })
    avatarId: string;

    @Column({ name: 'ho_ten', type: 'varchar', length: 100, nullable: false })
    hoTen: string;

    @Column({ name: 'don_vi_id', type: 'bigint', nullable: true })
    donViId: number;

    @Column({ name: 'email', type: 'varchar', length: 100, nullable: true, unique: true })
    email: string;

    @Column({ name: 'so_dien_thoai', type: 'varchar', length: 15, nullable: true })
    soDienThoai: string;

    @Column({ name: 'quan_nhan_id', type: 'varchar', length: 20, nullable: true, unique: true })
    quanNhanId: string;

    // @Column({ name: 'trang_thai_dang_nhap_id', type: 'bigint', nullable: false })
    // trangThaiDangNhapId: number;

    @Column({ type: 'enum', enum: USER_STATUS, default: USER_STATUS.ACTIVE })
    status: USER_STATUS;

    @Column({ name: 'lan_dang_nhap_cuoi', type: 'timestamp with time zone', nullable: true })
    lanDangNhapCuoi: Date;

    @Column({ name: 'so_lan_dang_nhap_sai', type: 'integer', default: 0 })
    soLanDangNhapSai: number;

    @Column({ name: 'thoi_gian_khoa_tai_khoan', type: 'timestamp with time zone', nullable: true })
    thoiGianKhoaTaiKhoan: Date;

    @Column({ name: 'yeu_cau_doi_mat_khau', type: 'boolean', default: false })
    yeuCauDoiMatKhau: boolean;

    /* RELATION */
    @OneToOne(() => AccountEntity, { createForeignKeyConstraints: false })
    @JoinColumn({ name: 'account_id', referencedColumnName: 'id' })
    account: Relation<AccountEntity>;

    @OneToOne(() => DuLieuTepTinEntity, { createForeignKeyConstraints: false })
    @JoinColumn({ name: 'avatar_id', referencedColumnName: 'id' })
    avatar?: Relation<DuLieuTepTinEntity>;

    @ManyToOne(() => GiaTriDanhMucEntity, { createForeignKeyConstraints: false })
    @JoinColumn({ name: 'don_vi_id', referencedColumnName: 'id' })
    donVi: Relation<GiaTriDanhMucEntity>;

    @OneToOne(() => QuanNhanEntity, { createForeignKeyConstraints: false })
    @JoinColumn({ name: 'quan_nhan_id', referencedColumnName: 'soHieuQuanNhan' })
    quanNhan: Relation<QuanNhanEntity>;

    // @ManyToOne(() => GiaTriDanhMucEntity, { createForeignKeyConstraints: false })
    // @JoinColumn({ name: 'trang_thai_dang_nhap_id', referencedColumnName: 'id' })
    // trangThaiDangNhap: Relation<GiaTriDanhMucEntity>;

    // Multiple roles relationship through users_roles table
    @OneToMany(() => UsersRoleEntity, (userRole) => userRole.user, {
        cascade: true,
        createForeignKeyConstraints: false,
    })
    userRoles: Relation<UsersRoleEntity[]>;
}
