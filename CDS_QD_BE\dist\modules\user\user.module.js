"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"UserModule",{enumerable:true,get:function(){return UserModule}});const _common=require("@nestjs/common");const _mediaservice=require("../media/media.service");const _usercontroller=require("./user.controller");const _userservice=require("./user.service");const _usersroleservice=require("./users-role.service");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}let UserModule=class UserModule{};UserModule=_ts_decorate([(0,_common.Module)({imports:[],controllers:[_usercontroller.UserController],providers:[_userservice.UserService,_usersroleservice.UsersRoleService,_mediaservice.MediaService]})],UserModule);
//# sourceMappingURL=user.module.js.map