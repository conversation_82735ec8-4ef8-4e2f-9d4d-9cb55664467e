"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"UsersRoleService",{enumerable:true,get:function(){return UsersRoleService}});const _common=require("@nestjs/common");const _databaseservice=require("../../database/typeorm/database.service");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let UsersRoleService=class UsersRoleService{async assignRolesToUser(userId,roleIds){const user=await this.database.user.findOneBy({id:userId});if(!user){throw new _common.HttpException("Không tìm thấy người dùng",404)}const roles=await this.database.role.findByIds(roleIds);if(roles.length!==roleIds.length){throw new _common.HttpException("Một hoặc nhiều role không tồn tại",400)}await this.database.usersRole.delete({userId});const userRoles=roleIds.map(roleId=>({userId,roleId}));return this.database.usersRole.save(userRoles)}async addRoleToUser(userId,roleId){const user=await this.database.user.findOneBy({id:userId});if(!user){throw new _common.HttpException("Không tìm thấy người dùng",404)}const role=await this.database.role.findOneBy({id:roleId});if(!role){throw new _common.HttpException("Không tìm thấy role",404)}const existingUserRole=await this.database.usersRole.findOne({where:{userId,roleId}});if(existingUserRole){throw new _common.HttpException("Người dùng đã có role này",400)}return this.database.usersRole.save({userId,roleId})}async removeRoleFromUser(userId,roleId){const result=await this.database.usersRole.delete({userId,roleId});if(result.affected===0){throw new _common.HttpException("Không tìm thấy quan hệ user-role để xóa",404)}return result}async getUserRoles(userId){return this.database.usersRole.find({where:{userId},relations:["role"]})}async getRoleUsers(roleId){return this.database.usersRole.find({where:{roleId},relations:["user"]})}async userHasRole(userId,roleId){const userRole=await this.database.usersRole.findOne({where:{userId,roleId}});return!!userRole}async bulkAssignRoles(userIds,roleIds){const userRoles=[];for(const userId of userIds){for(const roleId of roleIds){userRoles.push({userId,roleId})}}await this.database.usersRole.delete({userId:{$in:userIds}});return this.database.usersRole.save(userRoles)}async removeAllUserRoles(userId){return this.database.usersRole.delete({userId})}async removeAllRoleUsers(roleId){return this.database.usersRole.delete({roleId})}constructor(database){this.database=database}};UsersRoleService=_ts_decorate([(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _databaseservice.DatabaseService==="undefined"?Object:_databaseservice.DatabaseService])],UsersRoleService);
//# sourceMappingURL=users-role.service.js.map