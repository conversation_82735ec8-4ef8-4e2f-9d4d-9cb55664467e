/* eslint-disable @typescript-eslint/no-unused-vars */
import { Column, Entity, JoinTable, ManyToMany, OneToMany, PrimaryGeneratedColumn, Relation } from 'typeorm';
import { PermissionEntity } from '~/database/typeorm/entities/permission.entity';
import { UsersRoleEntity } from './usersRole.entity';
import { AbstractEntity } from './abstract.entity';

@Entity({ name: 'roles' })
export class RoleEntity extends AbstractEntity {
    @PrimaryGeneratedColumn('increment', { name: 'id', type: 'int', unsigned: true })
    id: number;

    @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
    name: string;

    @Column({ name: 'description', type: 'varchar', length: 255, nullable: true })
    description: string;

    /* RELATION */
    @ManyToMany(() => PermissionEntity, (permissions) => permissions.role, {
        onDelete: 'NO ACTION',
        onUpdate: 'CASCADE',
        createForeignKeyConstraints: false,
    })
    @JoinTable({
        name: 'roles_permissions',
        joinColumn: { name: 'role_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'permission_id', referencedColumnName: 'id' },
    })
    permissions: Relation<PermissionEntity>[];

    // Many-to-many relationship with users through users_roles table (unified multiple roles system)
    @OneToMany(() => UsersRoleEntity, (userRole) => userRole.role, {
        cascade: true,
        createForeignKeyConstraints: false,
    })
    userRoles: Relation<UsersRoleEntity[]>;
}
