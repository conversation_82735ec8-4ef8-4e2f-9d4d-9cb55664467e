import { HttpException, Injectable } from '@nestjs/common';
import { DatabaseService } from '~/database/typeorm/database.service';
import { MediaService } from '~/modules/media/media.service';
import { TokenService, UtilService } from '~/shared/services';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { AssignRolesDto } from './dto/assign-roles.dto';

@Injectable()
export class UserService {
    constructor(
        private readonly tokenService: TokenService,
        private readonly utilService: UtilService,
        private readonly mediaService: MediaService,
        private readonly database: DatabaseService,
    ) {}

    async create(createUserDto: CreateUserDto) {
        const { username, password, roleIds, ...rest } = createUserDto;
        const { salt, hash } = this.tokenService.hashPassword(createUserDto.password);
        const account = await this.database.account.save(
            this.database.account.create({
                username: createUserDto.username,
                password: hash,
                salt,
            }),
        );

        if (!account) {
            throw new HttpException('Cannot create account', 400);
        }

        const user = await this.database.user.save(this.database.user.create({ ...rest, accountId: account.id }));
        if (!user) {
            throw new HttpException('Cannot create user', 400);
        }

        // Handle multiple roles if provided
        if (roleIds && roleIds.length > 0) {
            const userRoles = roleIds.map((roleId) => ({
                userId: user.id,
                roleId,
            }));
            await this.database.usersRole.save(userRoles);
        }

        return {
            data: {
                account,
                user: await this.findOne(user.id), // Return user with all roles
            },
        };
    }

    async findAll(queries: { page: number; perPage: number; search: string; sortBy: string }) {
        const { builder, take, pagination } = this.utilService.getQueryBuilderAndPagination(this.database.user, queries);

        if (!this.utilService.isEmpty(queries.search)) {
            builder.andWhere('(entity.hoTen ILIKE :search OR entity.email ILIKE :search)', { search: `%${queries.search}%` });
        }

        // Include only multiple roles
        builder.leftJoinAndSelect('entity.userRoles', 'userRoles');
        builder.leftJoinAndSelect('userRoles.role', 'userRole');
        builder.leftJoinAndSelect('entity.avatar', 'avatar');
        builder.select(['entity', 'userRoles', 'userRole.id', 'userRole.name', 'avatar.id']);

        const [result, total] = await builder.getManyAndCount();
        const totalPages = Math.ceil(total / take);
        return {
            data: result,
            pagination: {
                ...pagination,
                totalRecords: total,
                totalPages: totalPages,
            },
        };
    }

    findOne(id: number) {
        return this.database.user.findOneUserWithAllRolesById(id);
    }

    // Get all roles for a user (both single and multiple)
    async getAllUserRoles(userId: number) {
        return this.database.user.getAllUserRoles(userId);
    }

    // Assign multiple roles to a user
    async assignRolesToUser(userId: number, roleIds: number[]) {
        const user = await this.database.user.findOneBy({ id: userId });
        if (!user) {
            throw new HttpException('Không tìm thấy người dùng', 404);
        }

        // Remove existing user roles to avoid duplicates
        await this.database.usersRole.delete({ userId });

        // Add new roles
        const userRoles = roleIds.map((roleId) => ({
            userId,
            roleId,
        }));

        await this.database.usersRole.save(userRoles);

        return this.findOne(userId);
    }

    // Remove a specific role from user
    async removeRoleFromUser(userId: number, roleId: number) {
        const user = await this.database.user.findOneBy({ id: userId });
        if (!user) {
            throw new HttpException('Không tìm thấy người dùng', 404);
        }

        await this.database.usersRole.delete({ userId, roleId });

        return this.findOne(userId);
    }

    async update(id: number, updateUserDto: UpdateUserDto) {
        const { username, password, roleIds, ...rest } = updateUserDto;
        const user = await this.database.user.findOneBy({ id });
        if (!user) {
            throw new HttpException('Không tìm thấy người dùng', 404);
        }

        if (password) {
            const { salt, hash } = this.tokenService.hashPassword(updateUserDto.password);
            this.database.account.update({ id: user.accountId }, { password: hash, salt });
        }

        // Handle multiple roles if provided
        if (roleIds && roleIds.length > 0) {
            // Remove existing user roles
            await this.database.usersRole.delete({ userId: id });

            // Add new roles
            const userRoles = roleIds.map((roleId) => ({
                userId: id,
                roleId,
            }));
            await this.database.usersRole.save(userRoles);
        }

        await this.database.user.update({ id }, rest);
        return this.findOne(id); // Return user with all roles
    }

    async remove(id: number) {
        const user = await this.database.user.findOneBy({ id });
        if (!user) {
            throw new HttpException('Không tìm thấy người dùng', 404);
        }

        // remove user
        await this.database.user.delete({ id });
        // remove account
        await this.database.account.delete({ id: user.accountId });
        // remove media
        if (user.avatar?.id) {
            await this.mediaService.remove(user.avatar.id);
        }

        return true;
    }
}
