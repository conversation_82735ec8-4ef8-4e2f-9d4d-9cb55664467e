{"version": 3, "sources": ["../../../../src/database/typeorm/entities/role.entity.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\nimport { Column, Entity, JoinTable, ManyToMany, OneToMany, PrimaryGeneratedColumn, Relation } from 'typeorm';\nimport { PermissionEntity } from '~/database/typeorm/entities/permission.entity';\nimport { UsersRoleEntity } from './usersRole.entity';\nimport { AbstractEntity } from './abstract.entity';\n\n@Entity({ name: 'roles' })\nexport class RoleEntity extends AbstractEntity {\n    @PrimaryGeneratedColumn('increment', { name: 'id', type: 'int', unsigned: true })\n    id: number;\n\n    @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })\n    name: string;\n\n    @Column({ name: 'description', type: 'varchar', length: 255, nullable: true })\n    description: string;\n\n    /* RELATION */\n    @ManyToMany(() => PermissionEntity, (permissions) => permissions.role, {\n        onDelete: 'NO ACTION',\n        onUpdate: 'CASCADE',\n        createForeignKeyConstraints: false,\n    })\n    @JoinTable({\n        name: 'roles_permissions',\n        joinColumn: { name: 'role_id', referencedColumnName: 'id' },\n        inverseJoinColumn: { name: 'permission_id', referencedColumnName: 'id' },\n    })\n    permissions: Relation<PermissionEntity>[];\n\n    // Many-to-many relationship with users through users_roles table (unified multiple roles system)\n    @OneToMany(() => UsersRoleEntity, (userRole) => userRole.role, {\n        cascade: true,\n        createForeignKeyConstraints: false,\n    })\n    userRoles: Relation<UsersRoleEntity[]>;\n}\n"], "names": ["RoleEntity", "AbstractEntity", "name", "type", "unsigned", "length", "nullable", "PermissionEntity", "permissions", "role", "onDelete", "onUpdate", "createForeignKeyConstraints", "joinColumn", "referencedColumnName", "inverseJoinColumn", "UsersRoleEntity", "userRole", "cascade"], "mappings": "oGAOaA,oDAAAA,qCANsF,2CAClE,sDACD,oDACD,kkBAGxB,IAAA,AAAMA,WAAN,MAAMA,mBAAmBC,8BAAc,CA6B9C,iEA5B2CC,KAAM,KAAMC,KAAM,MAAOC,SAAU,gHAGhEF,KAAM,OAAQC,KAAM,UAAWE,OAAQ,IAAKC,SAAU,mHAGtDJ,KAAM,cAAeC,KAAM,UAAWE,OAAQ,IAAKC,SAAU,gIAIrDC,kCAAgB,CAAGC,aAAgBA,YAAYC,IAAI,EACjEC,SAAU,YACVC,SAAU,UACVC,4BAA6B,gCAG7BV,KAAM,oBACNW,WAAY,CAAEX,KAAM,UAAWY,qBAAsB,IAAK,EAC1DC,kBAAmB,CAAEb,KAAM,gBAAiBY,qBAAsB,IAAK,2HAK1DE,gCAAe,CAAGC,UAAaA,SAASR,IAAI,EACzDS,QAAS,KACTN,4BAA6B,4LA3B3BV,KAAM"}