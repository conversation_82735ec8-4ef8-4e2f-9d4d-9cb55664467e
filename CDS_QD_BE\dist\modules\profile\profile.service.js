"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"ProfileService",{enumerable:true,get:function(){return ProfileService}});const _common=require("@nestjs/common");const _accountrepository=require("../../database/typeorm/repositories/account.repository");const _userrepository=require("../../database/typeorm/repositories/user.repository");const _services=require("../../shared/services");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let ProfileService=class ProfileService{findOne(id){const builder=this.userRepository.createQueryBuilder("user");builder.leftJoinAndSelect("user.userRoles","roles");builder.leftJoinAndSelect("roles.role","role");builder.leftJoinAndSelect("role.permissions","permission");builder.leftJoinAndSelect("user.avatar","avatar");builder.where("user.id = :id",{id});return builder.getOne()}update(id,updateProfileDto){return this.userRepository.update(id,updateProfileDto)}async changePassword(id,updateProfileDto){if(updateProfileDto.new_password!==updateProfileDto.confirm_password){throw new _common.BadRequestException("Mật khẩu mới không khớp")}const user=await this.userRepository.findOneBy({id});if(!user){throw new _common.BadRequestException("Không tìm thấy tài khoản")}const account=await this.accountRepository.findOneBy({id:user.accountId});const isMatch=await this.tokenService.isPasswordCorrect(updateProfileDto.old_password,account.password);if(!isMatch){throw new _common.BadRequestException("Mật khẩu cũ không đúng")}const{salt,hash}=this.tokenService.hashPassword(updateProfileDto.new_password);const res=await this.accountRepository.update({id:user.accountId},{password:hash,salt});return res}constructor(userRepository,accountRepository,tokenService){this.userRepository=userRepository;this.accountRepository=accountRepository;this.tokenService=tokenService}};ProfileService=_ts_decorate([(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _userrepository.UserRepository==="undefined"?Object:_userrepository.UserRepository,typeof _accountrepository.AccountRepository==="undefined"?Object:_accountrepository.AccountRepository,typeof _services.TokenService==="undefined"?Object:_services.TokenService])],ProfileService);
//# sourceMappingURL=profile.service.js.map