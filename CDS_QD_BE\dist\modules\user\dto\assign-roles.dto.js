"use strict";Object.defineProperty(exports,"__esModule",{value:true});function _export(target,all){for(var name in all)Object.defineProperty(target,name,{enumerable:true,get:Object.getOwnPropertyDescriptor(all,name).get})}_export(exports,{get AssignRolesDto(){return AssignRolesDto},get RemoveRoleDto(){return RemoveRoleDto}});const _swagger=require("@nestjs/swagger");const _classvalidator=require("class-validator");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let AssignRolesDto=class AssignRolesDto{};_ts_decorate([(0,_swagger.ApiProperty)({description:"Array of role IDs to assign to user",example:[1,2,3],type:[Number]}),(0,_classvalidator.IsArray)({message:"Role IDs phải là một mảng"}),(0,_classvalidator.IsNotEmpty)({message:"Role IDs không được để trống"}),(0,_classvalidator.IsNumber)({},{each:true,message:"Mỗi role ID phải là một số"}),_ts_metadata("design:type",Array)],AssignRolesDto.prototype,"roleIds",void 0);let RemoveRoleDto=class RemoveRoleDto{};_ts_decorate([(0,_swagger.ApiProperty)({description:"Role ID to remove from user",example:1}),(0,_classvalidator.IsNotEmpty)({message:"Role ID không được để trống"}),(0,_classvalidator.IsNumber)({},{message:"Role ID phải là một số"}),_ts_metadata("design:type",Number)],RemoveRoleDto.prototype,"roleId",void 0);
//# sourceMappingURL=assign-roles.dto.js.map