"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"UserController",{enumerable:true,get:function(){return UserController}});const _common=require("@nestjs/common");const _swagger=require("@nestjs/swagger");const _permissiondecorator=require("../../common/decorators/permission.decorator");const _filterdto=require("../../common/dtos/filter.dto");const _createuserdto=require("./dto/create-user.dto");const _updateuserdto=require("./dto/update-user.dto");const _assignrolesdto=require("./dto/assign-roles.dto");const _userservice=require("./user.service");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}function _ts_param(paramIndex,decorator){return function(target,key){decorator(target,key,paramIndex)}}let UserController=class UserController{create(createUserDto){return this.userService.create(createUserDto)}findAll(queries){return this.userService.findAll({...queries})}findOne(id){return this.userService.findOne(+id)}update(id,updateUserDto){return this.userService.update(+id,updateUserDto)}remove(id){return this.userService.remove(+id)}getUserRoles(id){return this.userService.getAllUserRoles(+id)}assignRolesToUser(id,assignRolesDto){return this.userService.assignRolesToUser(+id,assignRolesDto.roleIds)}removeRoleFromUser(id,roleId){return this.userService.removeRoleFromUser(+id,+roleId)}constructor(userService){this.userService=userService}};_ts_decorate([(0,_permissiondecorator.Permission)("user:create"),(0,_common.Post)(),_ts_param(0,(0,_common.Body)()),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _createuserdto.CreateUserDto==="undefined"?Object:_createuserdto.CreateUserDto]),_ts_metadata("design:returntype",void 0)],UserController.prototype,"create",null);_ts_decorate([(0,_permissiondecorator.Permission)("user:findAll"),(0,_common.Get)(),(0,_swagger.ApiQuery)({type:_filterdto.FilterDto}),_ts_param(0,(0,_common.Query)()),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[void 0]),_ts_metadata("design:returntype",void 0)],UserController.prototype,"findAll",null);_ts_decorate([(0,_permissiondecorator.Permission)("user:findOne"),(0,_common.Get)(":id"),_ts_param(0,(0,_common.Param)("id")),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[String]),_ts_metadata("design:returntype",void 0)],UserController.prototype,"findOne",null);_ts_decorate([(0,_permissiondecorator.Permission)("user:update"),(0,_common.Patch)(":id"),_ts_param(0,(0,_common.Param)("id")),_ts_param(1,(0,_common.Body)()),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[String,typeof _updateuserdto.UpdateUserDto==="undefined"?Object:_updateuserdto.UpdateUserDto]),_ts_metadata("design:returntype",void 0)],UserController.prototype,"update",null);_ts_decorate([(0,_permissiondecorator.Permission)("user:remove"),(0,_common.Delete)(":id"),_ts_param(0,(0,_common.Param)("id")),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[String]),_ts_metadata("design:returntype",void 0)],UserController.prototype,"remove",null);_ts_decorate([(0,_permissiondecorator.Permission)("user:findRoles"),(0,_common.Get)(":id/roles"),(0,_swagger.ApiOperation)({summary:"Get all roles for a user"}),_ts_param(0,(0,_common.Param)("id")),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[String]),_ts_metadata("design:returntype",void 0)],UserController.prototype,"getUserRoles",null);_ts_decorate([(0,_permissiondecorator.Permission)("user:assignRoles"),(0,_common.Post)(":id/roles"),(0,_swagger.ApiOperation)({summary:"Assign multiple roles to a user"}),_ts_param(0,(0,_common.Param)("id")),_ts_param(1,(0,_common.Body)()),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[String,typeof _assignrolesdto.AssignRolesDto==="undefined"?Object:_assignrolesdto.AssignRolesDto]),_ts_metadata("design:returntype",void 0)],UserController.prototype,"assignRolesToUser",null);_ts_decorate([(0,_permissiondecorator.Permission)("user:removeRole"),(0,_common.Delete)(":id/roles/:roleId"),(0,_swagger.ApiOperation)({summary:"Remove a specific role from user"}),_ts_param(0,(0,_common.Param)("id")),_ts_param(1,(0,_common.Param)("roleId")),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[String,String]),_ts_metadata("design:returntype",void 0)],UserController.prototype,"removeRoleFromUser",null);UserController=_ts_decorate([(0,_swagger.ApiTags)("User"),(0,_swagger.ApiBearerAuth)(),(0,_common.Controller)("user"),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _userservice.UserService==="undefined"?Object:_userservice.UserService])],UserController);
//# sourceMappingURL=user.controller.js.map