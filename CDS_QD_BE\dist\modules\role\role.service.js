"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"RoleService",{enumerable:true,get:function(){return RoleService}});const _common=require("@nestjs/common");const _rolerepository=require("../../database/typeorm/repositories/role.repository");const _databaseservice=require("../../database/typeorm/database.service");const _services=require("../../shared/services");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let RoleService=class RoleService{async create(createRoleDto){const entity=this.roleRepository.create({name:createRoleDto.name,description:createRoleDto.description});const result=await this.roleRepository.save(entity);if(createRoleDto.permissionIds?.length){this.roleRepository.createQueryBuilder().relation("permissions").of(result).add(createRoleDto.permissionIds)}return result}async findAll(query){const{take,skip,pagination}=this.utilService.getPagination(query);const builder=this.roleRepository.createQueryBuilder("entity");if(Number(query.perPage)!==0)builder.take(take).skip(skip);if(query.sortBy)builder.orderBy(this.utilService.getSortCondition("entity",query.sortBy));if(query.search)builder.andWhere("entity.name ILIKE :name",{name:`%${query.search}%`});const[result,total]=await builder.getManyAndCount();const totalPages=Math.ceil(total/take);return{data:result,pagination:{...pagination,totalRecords:total,totalPages:totalPages}}}async findOne(id){const result=await this.roleRepository.findOne({relations:["permissions"],where:{id}});if(!result)throw new _common.NotFoundException("Không tìm thấy quyền này!");const{permissions,...rest}=result;return{...rest,permissionIds:permissions.length>0?permissions.map(permission=>permission.id):[]}}async update(id,updateRoleDto){const{permissionIds,...rest}=updateRoleDto;const result=await this.roleRepository.update(id,rest);await this.roleRepository.removePermissions(id);if(permissionIds?.length){await this.roleRepository.createQueryBuilder().relation("permissions").of(id).add(permissionIds)}return result}async remove(id){const usersWithMultipleRoles=await this.database.usersRole.count({where:{roleId:id}});if(usersWithMultipleRoles>0){throw new _common.HttpException(`Kh\xf4ng thể x\xf3a role n\xe0y v\xec c\xf3 ${usersWithMultipleRoles} người d\xf9ng đang sử dụng`,400)}await this.roleRepository.removePermissions(id);return this.roleRepository.delete(id)}async getUsersByRole(roleId){const role=await this.roleRepository.findOneBy({id:roleId});if(!role){throw new _common.HttpException("Không tìm thấy role",404)}const userRoles=await this.database.usersRole.find({where:{roleId},relations:["user"]});return{role,users:userRoles.map(ur=>ur.user),totalUsers:userRoles.length}}async getRoleUsageStats(roleId){const role=await this.roleRepository.findOneBy({id:roleId});if(!role){throw new _common.HttpException("Không tìm thấy role",404)}const multipleRoleCount=await this.database.usersRole.count({where:{roleId}});return{roleId,roleName:role.name,usersWithMultipleRoles:multipleRoleCount,totalUsers:multipleRoleCount}}async canDeleteRole(roleId){const multipleRoleCount=await this.database.usersRole.count({where:{roleId}});return multipleRoleCount===0}constructor(roleRepository,utilService,database){this.roleRepository=roleRepository;this.utilService=utilService;this.database=database}};RoleService=_ts_decorate([(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _rolerepository.RoleRepository==="undefined"?Object:_rolerepository.RoleRepository,typeof _services.UtilService==="undefined"?Object:_services.UtilService,typeof _databaseservice.DatabaseService==="undefined"?Object:_databaseservice.DatabaseService])],RoleService);
//# sourceMappingURL=role.service.js.map