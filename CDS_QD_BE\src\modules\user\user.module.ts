import { Module } from '@nestjs/common';
import { MediaService } from '~/modules/media/media.service';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { UsersRoleService } from './users-role.service';

@Module({
    imports: [],
    controllers: [UserController],
    providers: [UserService, UsersRoleService, MediaService],
})
export class UserModule {}
