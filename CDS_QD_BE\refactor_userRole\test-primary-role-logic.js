// Test Primary Role Logic - <PERSON> h<PERSON><PERSON> c<PERSON> chế x<PERSON>c định role chính
console.log('🔍 Testing Primary Role Logic in Multiple Roles System');

// Mock data representing different user scenarios
const testUsers = [
    {
        id: 1,
        name: 'Admin User with Multiple Roles',
        roleId: 1,
        role: { id: 1, name: 'Admin' },
        userRoles: [
            { role: { id: 2, name: 'Editor' } },
            { role: { id: 3, name: 'Viewer' } }
        ]
    },
    {
        id: 2,
        name: 'User with Only Multiple Roles',
        roleId: null,
        role: null,
        userRoles: [
            { role: { id: 2, name: 'Editor' } },
            { role: { id: 3, name: 'Viewer' } },
            { role: { id: 4, name: 'Manager' } }
        ]
    },
    {
        id: 3,
        name: 'User with Duplicate Roles',
        roleId: 2,
        role: { id: 2, name: 'Editor' },
        userRoles: [
            { role: { id: 2, name: 'Editor' } },  // Duplicate
            { role: { id: 3, name: 'Viewer' } }
        ]
    },
    {
        id: 4,
        name: 'User with Only Single Role',
        roleId: 3,
        role: { id: 3, name: 'Viewer' },
        userRoles: []
    },
    {
        id: 5,
        name: 'User with No Roles',
        roleId: null,
        role: null,
        userRoles: []
    }
];

// Simulate AuthService.login() primary role logic
function getPrimaryRoleFromLogin(user) {
    const allRoles = getAllUserRoles(user);
    return user.role || (allRoles.length > 0 ? allRoles[0] : null);
}

// Simulate PermissionGuard primary role logic
function getPrimaryRoleFromPermissionGuard(user) {
    return user.roleId || user.userRoles?.[0]?.role?.id || null;
}

// Simulate UserRepository.getAllUserRoles()
function getAllUserRoles(user) {
    const roles = [];
    
    // Add single role if exists
    if (user.role) {
        roles.push(user.role);
    }

    // Add multiple roles if exists
    if (user.userRoles && user.userRoles.length > 0) {
        user.userRoles.forEach(userRole => {
            if (userRole.role) {
                roles.push(userRole.role);
            }
        });
    }

    // Remove duplicates based on role ID
    const uniqueRoles = roles.filter((role, index, self) => 
        index === self.findIndex(r => r.id === role.id)
    );

    return uniqueRoles;
}

// Simulate PermissionGuard.getAllUserRoleIds()
function getAllUserRoleIds(user) {
    const roleIds = [];

    if (user.roleId) {
        roleIds.push(user.roleId);
    }

    if (user.userRoles && user.userRoles.length > 0) {
        user.userRoles.forEach(userRole => {
            if (userRole.role?.id) {
                roleIds.push(userRole.role.id);
            }
        });
    }

    return [...new Set(roleIds)]; // Remove duplicates
}

// Mock permissions for demonstration
const mockPermissions = {
    1: ['admin.all'],                           // Admin
    2: ['user.read', 'user.write'],            // Editor  
    3: ['user.read'],                          // Viewer
    4: ['user.manage', 'user.delete']          // Manager
};

// Simulate permission aggregation
function getAggregatedPermissions(roleIds) {
    const permissions = new Set();
    roleIds.forEach(roleId => {
        if (mockPermissions[roleId]) {
            mockPermissions[roleId].forEach(perm => permissions.add(perm));
        }
    });
    return Array.from(permissions);
}

// Test each user scenario
console.log('\n📊 Testing Primary Role Logic for Different User Scenarios:\n');

testUsers.forEach((user, index) => {
    console.log(`${index + 1}. ${user.name}`);
    console.log(`   User ID: ${user.id}`);
    console.log(`   Single Role: ${user.role ? `${user.role.name} (ID: ${user.role.id})` : 'None'}`);
    console.log(`   Multiple Roles: [${user.userRoles.map(ur => `${ur.role.name} (${ur.role.id})`).join(', ')}]`);
    
    // Test primary role logic
    const primaryFromLogin = getPrimaryRoleFromLogin(user);
    const primaryIdFromGuard = getPrimaryRoleFromPermissionGuard(user);
    const allRoles = getAllUserRoles(user);
    const allRoleIds = getAllUserRoleIds(user);
    const aggregatedPermissions = getAggregatedPermissions(allRoleIds);
    
    console.log(`   
   📋 Results:
   - Primary Role (Login): ${primaryFromLogin ? `${primaryFromLogin.name} (ID: ${primaryFromLogin.id})` : 'None'}
   - Primary Role ID (Guard): ${primaryIdFromGuard || 'None'}
   - All Roles: [${allRoles.map(r => `${r.name} (${r.id})`).join(', ')}]
   - All Role IDs: [${allRoleIds.join(', ')}]
   - Aggregated Permissions: [${aggregatedPermissions.join(', ')}]
   `);
});

// Test Login Response Structure
console.log('\n🔐 Testing Login Response Structure:\n');

function simulateLoginResponse(user) {
    const allRoles = getAllUserRoles(user);
    const primaryRole = getPrimaryRoleFromLogin(user);
    
    return {
        result: true,
        message: 'Login successfully',
        data: {
            id: user.id,
            role: primaryRole,      // Backward compatibility
            roles: allRoles,        // New multiple roles
        }
    };
}

testUsers.slice(0, 3).forEach(user => {
    const loginResponse = simulateLoginResponse(user);
    console.log(`Login Response for ${user.name}:`);
    console.log(`   role: ${loginResponse.data.role ? loginResponse.data.role.name : 'null'}`);
    console.log(`   roles: [${loginResponse.data.roles.map(r => r.name).join(', ')}]`);
    console.log('');
});

// Test Permission Guard Headers
console.log('\n🛡️  Testing Permission Guard Headers:\n');

function simulatePermissionGuardHeaders(user) {
    const roleIds = getAllUserRoleIds(user);
    const primaryRoleId = user.roleId || roleIds[0];
    
    return {
        '_roleId': primaryRoleId?.toString() || '',
        '_allRoleIds': roleIds.join(','),
        '_fullName': user.name
    };
}

testUsers.slice(0, 3).forEach(user => {
    const headers = simulatePermissionGuardHeaders(user);
    console.log(`Headers for ${user.name}:`);
    console.log(`   _roleId: "${headers._roleId}"`);
    console.log(`   _allRoleIds: "${headers._allRoleIds}"`);
    console.log('');
});

// Test Edge Cases
console.log('\n⚠️  Testing Edge Cases:\n');

const edgeCases = [
    {
        name: 'User with corrupted userRoles',
        roleId: 1,
        role: { id: 1, name: 'Admin' },
        userRoles: [
            { role: null },  // Corrupted
            { role: { id: 2, name: 'Editor' } }
        ]
    },
    {
        name: 'User with empty role object',
        roleId: null,
        role: null,
        userRoles: [
            { role: {} },  // Empty role object
            { role: { id: 2, name: 'Editor' } }
        ]
    }
];

edgeCases.forEach(user => {
    console.log(`Testing: ${user.name}`);
    try {
        const allRoles = getAllUserRoles(user);
        const primaryRole = getPrimaryRoleFromLogin(user);
        console.log(`   ✅ Handled gracefully`);
        console.log(`   Primary Role: ${primaryRole ? primaryRole.name : 'None'}`);
        console.log(`   All Roles: [${allRoles.map(r => r.name).join(', ')}]`);
    } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
    }
    console.log('');
});

// Summary and Recommendations
console.log('\n📝 Summary and Analysis:\n');

console.log('✅ Current Primary Role Logic:');
console.log('   1. Single Role (roleId) has highest priority');
console.log('   2. First role in userRoles array is fallback');
console.log('   3. null if no roles exist');
console.log('   4. Logic is consistent across AuthService and PermissionGuard');

console.log('\n✅ Strengths:');
console.log('   - Backward compatible');
console.log('   - Simple and predictable');
console.log('   - Performance efficient');
console.log('   - Handles edge cases gracefully');

console.log('\n⚠️  Limitations:');
console.log('   - No user control over primary role selection');
console.log('   - Depends on array order for multiple roles');
console.log('   - Logic is implicit, not explicit');
console.log('   - Cannot change primary role without changing single role');

console.log('\n🚀 Recommendations:');
console.log('   - Short term: Document current logic clearly');
console.log('   - Long term: Consider adding isPrimary flag to users_roles');
console.log('   - Add API endpoints for primary role management');
console.log('   - Implement role priority system for better fallback logic');

console.log('\n🎯 Conclusion:');
console.log('   Current system works well for backward compatibility');
console.log('   but lacks flexibility for advanced use cases.');
console.log('   Consider gradual enhancement based on business needs.');
