"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"CreateUserDto",{enumerable:true,get:function(){return CreateUserDto}});const _swagger=require("@nestjs/swagger");const _classtransformer=require("class-transformer");const _classvalidator=require("class-validator");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let CreateUserDto=class CreateUserDto{};_ts_decorate([(0,_swagger.ApiProperty)(),(0,_classvalidator.IsNotEmpty)({message:"Tên đăng nhập không được để trống"}),_ts_metadata("design:type",String)],CreateUserDto.prototype,"username",void 0);_ts_decorate([(0,_swagger.ApiProperty)(),(0,_classvalidator.IsNotEmpty)({message:"Mật khẩu không được để trống"}),_ts_metadata("design:type",String)],CreateUserDto.prototype,"password",void 0);_ts_decorate([(0,_swagger.ApiProperty)(),(0,_classvalidator.IsNotEmpty)({message:"Email không được để trống"}),(0,_classvalidator.IsEmail)({},{message:"Email không hợp lệ"}),_ts_metadata("design:type",String)],CreateUserDto.prototype,"email",void 0);_ts_decorate([(0,_swagger.ApiProperty)(),(0,_classvalidator.IsOptional)(),_ts_metadata("design:type",Number)],CreateUserDto.prototype,"roleId",void 0);_ts_decorate([(0,_swagger.ApiProperty)({description:"Array of role IDs for multiple roles (optional)",example:[1,2,3],type:[Number],required:false}),(0,_classvalidator.IsOptional)(),(0,_classvalidator.IsArray)({message:"Role IDs phải là một mảng"}),(0,_classvalidator.IsNumber)({},{each:true,message:"Mỗi role ID phải là một số"}),_ts_metadata("design:type",Array)],CreateUserDto.prototype,"roleIds",void 0);_ts_decorate([(0,_swagger.ApiProperty)(),(0,_classvalidator.IsOptional)(),_ts_metadata("design:type",Number)],CreateUserDto.prototype,"departmentId",void 0);_ts_decorate([(0,_swagger.ApiProperty)(),(0,_classvalidator.IsOptional)(),_ts_metadata("design:type",String)],CreateUserDto.prototype,"fullName",void 0);_ts_decorate([(0,_swagger.ApiProperty)(),(0,_classvalidator.IsOptional)(),_ts_metadata("design:type",String)],CreateUserDto.prototype,"areaCode",void 0);_ts_decorate([(0,_swagger.ApiProperty)(),(0,_classvalidator.IsOptional)(),_ts_metadata("design:type",String)],CreateUserDto.prototype,"phone",void 0);_ts_decorate([(0,_swagger.ApiProperty)(),(0,_classvalidator.IsOptional)(),_ts_metadata("design:type",String)],CreateUserDto.prototype,"address",void 0);_ts_decorate([(0,_swagger.ApiProperty)(),(0,_classvalidator.IsOptional)(),_ts_metadata("design:type",String)],CreateUserDto.prototype,"birthday",void 0);_ts_decorate([(0,_swagger.ApiProperty)(),(0,_classvalidator.IsOptional)(),(0,_classtransformer.Transform)(({value})=>value?.toLowerCase()),_ts_metadata("design:type",String)],CreateUserDto.prototype,"gender",void 0);
//# sourceMappingURL=create-user.dto.js.map